/**
 * Advanced Forecast JavaScript
 * Handles forecast generation, visualization, and user interactions
 */

let currentForecastData = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeForecastPage();
});

function initializeForecastPage() {
    console.log('🚀 Initializing Advanced Forecast Page');
    
    // Set up form submission
    const forecastForm = document.getElementById('forecastForm');
    if (forecastForm) {
        forecastForm.addEventListener('submit', handleForecastSubmission);
    }
    
    // Set default dates
    setDefaultDates();
    
    // Set up date validation
    setupDateValidation();
}

function setDefaultDates() {
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    
    if (startDateInput && endDateInput) {
        // Set end date to today
        const today = new Date();
        endDateInput.value = today.toISOString().split('T')[0];
        
        // Set start date to 6 months ago
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
        startDateInput.value = sixMonthsAgo.toISOString().split('T')[0];
    }
}

function setupDateValidation() {
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    
    if (startDateInput && endDateInput) {
        startDateInput.addEventListener('change', function() {
            if (this.value && endDateInput.value && this.value > endDateInput.value) {
                endDateInput.value = this.value;
            }
        });
        
        endDateInput.addEventListener('change', function() {
            if (this.value && startDateInput.value && this.value < startDateInput.value) {
                startDateInput.value = this.value;
            }
        });
    }
}

function setPreset(model, days) {
    console.log(`🎯 Setting preset: ${model}, ${days} days`);
    
    const modelSelect = document.getElementById('modelSelect');
    const forecastDays = document.getElementById('forecastDays');
    
    if (modelSelect) {
        modelSelect.value = model;
    }
    
    if (forecastDays) {
        forecastDays.value = days.toString();
    }
    
    // Highlight the form briefly
    const formCard = document.querySelector('.forecast-form-card');
    if (formCard) {
        formCard.style.transform = 'scale(1.02)';
        setTimeout(() => {
            formCard.style.transform = 'scale(1)';
        }, 200);
    }
}

async function handleForecastSubmission(event) {
    event.preventDefault();
    
    console.log('📊 Generating forecast...');
    
    // Show loading overlay
    showLoading();
    
    // Get form data
    const formData = new FormData(event.target);
    const requestData = {
        model: formData.get('model'),
        days: parseInt(formData.get('days')),
        start_date: formData.get('start_date') || null,
        end_date: formData.get('end_date') || null
    };
    
    console.log('📤 Request data:', requestData);
    
    try {
        const response = await fetch('/api/forecast', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            console.log('✅ Forecast generated successfully');
            currentForecastData = result;
            displayForecastResults(result);
        } else {
            throw new Error(result.error || 'Unknown error occurred');
        }
        
    } catch (error) {
        console.error('❌ Forecast generation failed:', error);
        showError('Failed to generate forecast: ' + error.message);
    } finally {
        hideLoading();
    }
}

function displayForecastResults(data) {
    console.log('📈 Displaying forecast results');
    
    // Hide initial state and show results
    const initialState = document.getElementById('initialState');
    const resultsArea = document.getElementById('resultsArea');
    
    if (initialState) initialState.style.display = 'none';
    if (resultsArea) resultsArea.style.display = 'block';
    
    // Display chart
    displayForecastChart(data.chart);
    
    // Display summary
    displayForecastSummary(data.summary, data.model_name, data.forecast_days);
    
    // Display model information
    displayModelInfo(data.model_info);
    
    // Display detailed data table
    displayForecastTable(data.forecast_data);
    
    // Scroll to results
    resultsArea.scrollIntoView({ behavior: 'smooth' });
}

function displayForecastChart(chartBase64) {
    const chartImg = document.getElementById('forecastChart');
    const chartPlaceholder = document.getElementById('chartPlaceholder');
    
    if (chartImg && chartBase64) {
        chartImg.src = 'data:image/png;base64,' + chartBase64;
        chartImg.style.display = 'block';
        if (chartPlaceholder) chartPlaceholder.style.display = 'none';
    }
}

function displayForecastSummary(summary, modelName, forecastDays) {
    const summaryContainer = document.getElementById('forecastSummary');
    if (!summaryContainer) return;
    
    summaryContainer.innerHTML = `
        <div class="col-md-3">
            <div class="forecast-summary">
                <div class="forecast-value">$${summary.total_forecast.toLocaleString('en-US', {maximumFractionDigits: 0})}</div>
                <div class="forecast-label">Total Forecast</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="forecast-summary" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <div class="forecast-value">$${summary.avg_daily.toLocaleString('en-US', {maximumFractionDigits: 0})}</div>
                <div class="forecast-label">Daily Average</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="forecast-summary" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                <div class="forecast-value">$${summary.min_value.toLocaleString('en-US', {maximumFractionDigits: 0})}</div>
                <div class="forecast-label">Minimum</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="forecast-summary" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
                <div class="forecast-value">$${summary.max_value.toLocaleString('en-US', {maximumFractionDigits: 0})}</div>
                <div class="forecast-label">Maximum</div>
            </div>
        </div>
    `;
}

function displayModelInfo(modelInfo) {
    const modelInfoContainer = document.getElementById('modelInfo');
    if (!modelInfoContainer) return;
    
    let performanceHtml = '';
    if (modelInfo.performance) {
        const perf = modelInfo.performance;
        performanceHtml = `
            <div class="row mt-3">
                <div class="col-md-3">
                    <span class="model-info-badge">MAE: ${(perf.mae_percent || 0).toFixed(3)}%</span>
                </div>
                <div class="col-md-3">
                    <span class="model-info-badge">MAPE: ${(perf.mape || 0).toFixed(3)}%</span>
                </div>
                <div class="col-md-3">
                    <span class="model-info-badge">RMSE: ${(perf.rmse_percent || 0).toFixed(3)}%</span>
                </div>
                <div class="col-md-3">
                    <span class="model-info-badge">R²: ${(perf.r2 || 0).toFixed(4)}</span>
                </div>
            </div>
        `;
    }
    
    modelInfoContainer.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-robot me-2"></i>Model Type</h6>
                <p class="mb-2">${modelInfo.type || 'Unknown'}</p>
                
                <h6><i class="fas fa-info-circle me-2"></i>Description</h6>
                <p class="mb-0">${modelInfo.description || 'No description available'}</p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-chart-bar me-2"></i>Performance Metrics</h6>
                ${performanceHtml || '<p class="text-muted">Performance metrics not available</p>'}
            </div>
        </div>
    `;
}

function displayForecastTable(forecastData) {
    const tableBody = document.querySelector('#forecastTable tbody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    forecastData.forEach(item => {
        const date = new Date(item.date);
        const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.date}</td>
            <td class="fw-bold text-success">${item.formatted_value}</td>
            <td><span class="badge bg-light text-dark">${dayOfWeek}</span></td>
        `;
        tableBody.appendChild(row);
    });
}

function showLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
}

function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

function showError(message) {
    // Create and show error alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 10000; max-width: 400px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Error:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

function downloadForecast() {
    if (!currentForecastData) {
        showError('No forecast data available to download');
        return;
    }
    
    console.log('📥 Downloading forecast data...');
    
    // Create CSV content
    let csvContent = 'Date,Predicted Revenue,Day of Week\n';
    
    currentForecastData.forecast_data.forEach(item => {
        const date = new Date(item.date);
        const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
        csvContent += `${item.date},${item.value},${dayOfWeek}\n`;
    });
    
    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `forecast_${currentForecastData.model_name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    // Show success message
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 10000; max-width: 400px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        <strong>Success:</strong> Forecast data downloaded successfully!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

function copyToClipboard() {
    if (!currentForecastData) {
        showError('No forecast data available to copy');
        return;
    }
    
    console.log('📋 Copying forecast data to clipboard...');
    
    // Create text content
    let textContent = 'Date\tPredicted Revenue\tDay of Week\n';
    
    currentForecastData.forecast_data.forEach(item => {
        const date = new Date(item.date);
        const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
        textContent += `${item.date}\t${item.formatted_value}\t${dayOfWeek}\n`;
    });
    
    // Copy to clipboard
    navigator.clipboard.writeText(textContent).then(() => {
        // Show success message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-info alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 10000; max-width: 400px;';
        alertDiv.innerHTML = `
            <i class="fas fa-copy me-2"></i>
            <strong>Copied:</strong> Forecast data copied to clipboard!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }).catch(err => {
        console.error('Failed to copy to clipboard:', err);
        showError('Failed to copy data to clipboard');
    });
}
