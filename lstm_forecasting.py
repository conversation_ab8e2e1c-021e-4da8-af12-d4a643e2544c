#!/usr/bin/env python3
"""
LSTM Time Series Forecasting Model
Advanced LSTM implementation for daily revenue forecasting with target performance <5% MAE and MAPE
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.regularizers import l2
import tensorflow as tf
import warnings
import pickle
import json
from datetime import datetime, timedelta
import os

warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

class LSTMForecaster:
    """Advanced LSTM Forecasting Model with comprehensive evaluation"""
    
    def __init__(self, sequence_length=30, target_mae=5.0, target_mape=5.0):
        self.sequence_length = sequence_length
        self.target_mae = target_mae
        self.target_mape = target_mape
        self.model = None
        self.scaler = None
        self.feature_scaler = None
        self.training_history = None
        self.evaluation_results = {}
        
    def load_and_prepare_data(self):
        """Load and prepare data for LSTM training"""
        print("="*60)
        print("LOADING AND PREPARING DATA FOR LSTM")
        print("="*60)
        
        # Load the cleaned daily revenue time series
        print("📊 Loading daily revenue time series...")
        daily_ts = pd.read_csv('daily_revenue_timeseries.csv')
        daily_ts['Date'] = pd.to_datetime(daily_ts['Date'])
        daily_ts = daily_ts.sort_values('Date').reset_index(drop=True)
        
        print(f"✅ Loaded {len(daily_ts)} daily observations")
        print(f"   Date range: {daily_ts['Date'].min()} to {daily_ts['Date'].max()}")
        print(f"   Revenue range: ${daily_ts['DailyRevenue'].min():,.2f} to ${daily_ts['DailyRevenue'].max():,.2f}")
        
        # Create additional features for LSTM
        print("\n🔧 Creating advanced features...")
        daily_ts = self.create_advanced_features(daily_ts)
        
        # Handle missing dates (create continuous time series)
        print("\n📅 Creating continuous time series...")
        daily_ts = self.create_continuous_timeseries(daily_ts)
        
        print(f"✅ Final dataset: {len(daily_ts)} observations")
        return daily_ts
    
    def create_advanced_features(self, df):
        """Create advanced features for LSTM model"""
        df = df.copy()
        
        # Time-based features
        df['Year'] = df['Date'].dt.year
        df['Month'] = df['Date'].dt.month
        df['Day'] = df['Date'].dt.day
        df['DayOfWeek'] = df['Date'].dt.dayofweek
        df['DayOfYear'] = df['Date'].dt.dayofyear
        df['WeekOfYear'] = df['Date'].dt.isocalendar().week
        df['Quarter'] = df['Date'].dt.quarter
        
        # Cyclical encoding for time features
        df['Month_sin'] = np.sin(2 * np.pi * df['Month'] / 12)
        df['Month_cos'] = np.cos(2 * np.pi * df['Month'] / 12)
        df['DayOfWeek_sin'] = np.sin(2 * np.pi * df['DayOfWeek'] / 7)
        df['DayOfWeek_cos'] = np.cos(2 * np.pi * df['DayOfWeek'] / 7)
        df['DayOfYear_sin'] = np.sin(2 * np.pi * df['DayOfYear'] / 365)
        df['DayOfYear_cos'] = np.cos(2 * np.pi * df['DayOfYear'] / 365)
        
        # Lag features
        for lag in [1, 2, 3, 7, 14, 30]:
            df[f'Revenue_lag_{lag}'] = df['DailyRevenue'].shift(lag)
            df[f'Quantity_lag_{lag}'] = df['DailyQuantity'].shift(lag)
            df[f'Transactions_lag_{lag}'] = df['DailyTransactions'].shift(lag)
        
        # Rolling statistics
        for window in [7, 14, 30]:
            df[f'Revenue_rolling_mean_{window}'] = df['DailyRevenue'].rolling(window=window).mean()
            df[f'Revenue_rolling_std_{window}'] = df['DailyRevenue'].rolling(window=window).std()
            df[f'Revenue_rolling_min_{window}'] = df['DailyRevenue'].rolling(window=window).min()
            df[f'Revenue_rolling_max_{window}'] = df['DailyRevenue'].rolling(window=window).max()
        
        # Exponential moving averages
        for alpha in [0.1, 0.3, 0.5]:
            df[f'Revenue_ema_{alpha}'] = df['DailyRevenue'].ewm(alpha=alpha).mean()
        
        # Revenue ratios and differences
        df['Revenue_pct_change'] = df['DailyRevenue'].pct_change()
        df['Revenue_diff'] = df['DailyRevenue'].diff()
        df['Avg_transaction_value'] = df['DailyRevenue'] / df['DailyTransactions']
        df['Avg_items_per_transaction'] = df['DailyQuantity'] / df['DailyTransactions']
        
        print(f"   Created {len(df.columns) - 4} additional features")
        return df
    
    def create_continuous_timeseries(self, df):
        """Create continuous time series by filling missing dates"""
        # Create complete date range
        date_range = pd.date_range(start=df['Date'].min(), end=df['Date'].max(), freq='D')
        complete_df = pd.DataFrame({'Date': date_range})
        
        # Merge with existing data
        df_complete = complete_df.merge(df, on='Date', how='left')
        
        # Forward fill missing values for most features
        numeric_cols = df_complete.select_dtypes(include=[np.number]).columns
        df_complete[numeric_cols] = df_complete[numeric_cols].fillna(method='ffill')
        
        # For revenue-related columns, use interpolation
        revenue_cols = [col for col in df_complete.columns if 'Revenue' in col or 'Quantity' in col or 'Transaction' in col]
        for col in revenue_cols:
            if col in df_complete.columns:
                df_complete[col] = df_complete[col].interpolate(method='linear')
        
        # Fill any remaining NaN values
        df_complete = df_complete.fillna(method='bfill').fillna(method='ffill')
        
        return df_complete
    
    def prepare_sequences(self, data, target_col='DailyRevenue'):
        """Prepare sequences for LSTM training"""
        print(f"\n🔄 Preparing sequences (length={self.sequence_length})...")
        
        # Select features for training
        feature_cols = [col for col in data.columns if col not in ['Date']]
        
        # Scale the data
        self.scaler = MinMaxScaler()
        self.feature_scaler = MinMaxScaler()
        
        # Scale target variable
        target_data = data[target_col].values.reshape(-1, 1)
        scaled_target = self.scaler.fit_transform(target_data)
        
        # Scale features
        feature_data = data[feature_cols].values
        scaled_features = self.feature_scaler.fit_transform(feature_data)
        
        # Create sequences
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_features)):
            X.append(scaled_features[i-self.sequence_length:i])
            y.append(scaled_target[i, 0])
        
        X, y = np.array(X), np.array(y)
        
        print(f"✅ Created {len(X)} sequences")
        print(f"   Input shape: {X.shape}")
        print(f"   Output shape: {y.shape}")
        
        return X, y, data.iloc[self.sequence_length:]['Date'].values
    
    def split_data(self, X, y, dates, train_ratio=0.7, val_ratio=0.15):
        """Split data into train, validation, and test sets"""
        n_samples = len(X)
        train_size = int(n_samples * train_ratio)
        val_size = int(n_samples * val_ratio)
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        dates_train = dates[:train_size]
        
        X_val = X[train_size:train_size + val_size]
        y_val = y[train_size:train_size + val_size]
        dates_val = dates[train_size:train_size + val_size]
        
        X_test = X[train_size + val_size:]
        y_test = y[train_size + val_size:]
        dates_test = dates[train_size + val_size:]
        
        print(f"\n📊 Data split:")
        print(f"   Training: {len(X_train)} samples ({train_ratio*100:.1f}%)")
        print(f"   Validation: {len(X_val)} samples ({val_ratio*100:.1f}%)")
        print(f"   Testing: {len(X_test)} samples ({(1-train_ratio-val_ratio)*100:.1f}%)")
        
        return (X_train, y_train, dates_train), (X_val, y_val, dates_val), (X_test, y_test, dates_test)

    def build_lstm_model(self, input_shape):
        """Build advanced LSTM model architecture"""
        print(f"\n🏗️ Building LSTM model architecture...")
        print(f"   Input shape: {input_shape}")

        model = Sequential([
            # First LSTM layer with return sequences
            LSTM(128, return_sequences=True, input_shape=input_shape,
                 kernel_regularizer=l2(0.001), recurrent_regularizer=l2(0.001)),
            BatchNormalization(),
            Dropout(0.3),

            # Second LSTM layer with return sequences
            LSTM(64, return_sequences=True,
                 kernel_regularizer=l2(0.001), recurrent_regularizer=l2(0.001)),
            BatchNormalization(),
            Dropout(0.3),

            # Third LSTM layer without return sequences
            LSTM(32, return_sequences=False,
                 kernel_regularizer=l2(0.001), recurrent_regularizer=l2(0.001)),
            BatchNormalization(),
            Dropout(0.2),

            # Dense layers
            Dense(16, activation='relu', kernel_regularizer=l2(0.001)),
            Dropout(0.1),
            Dense(8, activation='relu', kernel_regularizer=l2(0.001)),
            Dense(1, activation='linear')
        ])

        # Compile model with advanced optimizer
        optimizer = Adam(learning_rate=0.001, beta_1=0.9, beta_2=0.999, epsilon=1e-8)
        model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])

        print("✅ Model architecture built successfully")
        print(f"   Total parameters: {model.count_params():,}")

        return model

    def train_model(self, train_data, val_data, epochs=200, batch_size=32):
        """Train the LSTM model with advanced callbacks"""
        X_train, y_train, _ = train_data
        X_val, y_val, _ = val_data

        print(f"\n🚀 Training LSTM model...")
        print(f"   Epochs: {epochs}")
        print(f"   Batch size: {batch_size}")

        # Build model
        self.model = self.build_lstm_model((X_train.shape[1], X_train.shape[2]))

        # Define callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=20,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1
            ),
            ModelCheckpoint(
                'models/best_lstm_model.h5',
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            )
        ]

        # Create models directory if it doesn't exist
        os.makedirs('models', exist_ok=True)

        # Train the model
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1,
            shuffle=True
        )

        self.training_history = history
        print("✅ Model training completed")

        return history

    def calculate_metrics(self, y_true, y_pred, dataset_name=""):
        """Calculate comprehensive performance metrics"""
        # Convert back to original scale
        y_true_orig = self.scaler.inverse_transform(y_true.reshape(-1, 1)).flatten()
        y_pred_orig = self.scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()

        # Calculate metrics
        mae = mean_absolute_error(y_true_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_true_orig, y_pred_orig))

        # Calculate percentage metrics
        mae_percent = (mae / np.mean(y_true_orig)) * 100
        mape = np.mean(np.abs((y_true_orig - y_pred_orig) / y_true_orig)) * 100
        rmse_percent = (rmse / np.mean(y_true_orig)) * 100

        # Calculate R²
        ss_res = np.sum((y_true_orig - y_pred_orig) ** 2)
        ss_tot = np.sum((y_true_orig - np.mean(y_true_orig)) ** 2)
        r2 = 1 - (ss_res / ss_tot)

        metrics = {
            'mae': mae,
            'rmse': rmse,
            'mae_percent': mae_percent,
            'mape': mape,
            'rmse_percent': rmse_percent,
            'r2': r2,
            'mean_actual': np.mean(y_true_orig),
            'std_actual': np.std(y_true_orig),
            'mean_predicted': np.mean(y_pred_orig),
            'std_predicted': np.std(y_pred_orig)
        }

        return metrics

    def evaluate_model(self, train_data, val_data, test_data):
        """Comprehensive model evaluation"""
        print(f"\n📊 EVALUATING LSTM MODEL PERFORMANCE")
        print("="*60)

        X_train, y_train, dates_train = train_data
        X_val, y_val, dates_val = val_data
        X_test, y_test, dates_test = test_data

        # Make predictions
        print("🔮 Making predictions...")
        train_pred = self.model.predict(X_train, verbose=0)
        val_pred = self.model.predict(X_val, verbose=0)
        test_pred = self.model.predict(X_test, verbose=0)

        # Calculate metrics for each dataset
        train_metrics = self.calculate_metrics(y_train, train_pred, "Training")
        val_metrics = self.calculate_metrics(y_val, val_pred, "Validation")
        test_metrics = self.calculate_metrics(y_test, test_pred, "Test")

        # Store evaluation results
        self.evaluation_results = {
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'test_metrics': test_metrics,
            'target_mae': self.target_mae,
            'target_mape': self.target_mape,
            'model_params': {
                'sequence_length': self.sequence_length,
                'total_parameters': self.model.count_params(),
                'architecture': 'Multi-layer LSTM with BatchNorm and Dropout'
            }
        }

        # Print results
        self.print_evaluation_results()

        # Create visualizations
        self.create_evaluation_plots(
            (y_train, train_pred, dates_train, "Training"),
            (y_val, val_pred, dates_val, "Validation"),
            (y_test, test_pred, dates_test, "Test")
        )

        return self.evaluation_results

    def print_evaluation_results(self):
        """Print comprehensive evaluation results"""
        print(f"\n📈 LSTM MODEL PERFORMANCE RESULTS")
        print("="*60)

        datasets = [
            ("Training", self.evaluation_results['train_metrics']),
            ("Validation", self.evaluation_results['val_metrics']),
            ("Test", self.evaluation_results['test_metrics'])
        ]

        for dataset_name, metrics in datasets:
            print(f"\n{dataset_name} Set Performance:")
            print(f"  MAE: ${metrics['mae']:,.2f} ({metrics['mae_percent']:.2f}%)")
            print(f"  MAPE: {metrics['mape']:.2f}%")
            print(f"  RMSE: ${metrics['rmse']:,.2f} ({metrics['rmse_percent']:.2f}%)")
            print(f"  R²: {metrics['r2']:.4f}")

            # Check target achievement
            mae_target_met = metrics['mae_percent'] < self.target_mae
            mape_target_met = metrics['mape'] < self.target_mape

            print(f"  Target Achievement:")
            print(f"    MAE <{self.target_mae}%: {'✅ ACHIEVED' if mae_target_met else '❌ NOT MET'}")
            print(f"    MAPE <{self.target_mape}%: {'✅ ACHIEVED' if mape_target_met else '❌ NOT MET'}")

        # Overall assessment
        test_metrics = self.evaluation_results['test_metrics']
        mae_achieved = test_metrics['mae_percent'] < self.target_mae
        mape_achieved = test_metrics['mape'] < self.target_mape

        print(f"\n🎯 OVERALL TARGET ASSESSMENT (Test Set):")
        print(f"   MAE Target (<{self.target_mae}%): {test_metrics['mae_percent']:.2f}% - {'✅ ACHIEVED' if mae_achieved else '❌ NOT MET'}")
        print(f"   MAPE Target (<{self.target_mape}%): {test_metrics['mape']:.2f}% - {'✅ ACHIEVED' if mape_achieved else '❌ NOT MET'}")

        if mae_achieved and mape_achieved:
            print(f"   🏆 EXCELLENT: Both targets achieved!")
        elif mae_achieved or mape_achieved:
            print(f"   ⚠️  PARTIAL: One target achieved")
        else:
            print(f"   ❌ NEEDS IMPROVEMENT: Neither target achieved")

    def create_evaluation_plots(self, *datasets):
        """Create comprehensive evaluation visualizations"""
        print(f"\n📊 Creating evaluation visualizations...")

        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('LSTM Model Performance Evaluation', fontsize=16, fontweight='bold')

        # Plot 1: Predictions vs Actual for Test Set
        y_test, test_pred, dates_test, _ = datasets[2]  # Test data
        y_test_orig = self.scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        test_pred_orig = self.scaler.inverse_transform(test_pred.reshape(-1, 1)).flatten()

        axes[0, 0].plot(dates_test, y_test_orig, label='Actual', color='blue', alpha=0.7)
        axes[0, 0].plot(dates_test, test_pred_orig, label='LSTM Prediction', color='red', alpha=0.7)
        axes[0, 0].set_title('Test Set: Actual vs Predicted Revenue')
        axes[0, 0].set_xlabel('Date')
        axes[0, 0].set_ylabel('Daily Revenue ($)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Residuals
        residuals = y_test_orig - test_pred_orig
        axes[0, 1].scatter(test_pred_orig, residuals, alpha=0.6, color='purple')
        axes[0, 1].axhline(y=0, color='red', linestyle='--')
        axes[0, 1].set_title('Residuals Plot (Test Set)')
        axes[0, 1].set_xlabel('Predicted Values')
        axes[0, 1].set_ylabel('Residuals')
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: Training History
        if self.training_history:
            axes[1, 0].plot(self.training_history.history['loss'], label='Training Loss', color='blue')
            axes[1, 0].plot(self.training_history.history['val_loss'], label='Validation Loss', color='red')
            axes[1, 0].set_title('Training History')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Loss (MSE)')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Performance Comparison
        datasets_names = ['Training', 'Validation', 'Test']
        mae_values = [self.evaluation_results['train_metrics']['mae_percent'],
                     self.evaluation_results['val_metrics']['mae_percent'],
                     self.evaluation_results['test_metrics']['mae_percent']]
        mape_values = [self.evaluation_results['train_metrics']['mape'],
                      self.evaluation_results['val_metrics']['mape'],
                      self.evaluation_results['test_metrics']['mape']]

        x = np.arange(len(datasets_names))
        width = 0.35

        axes[1, 1].bar(x - width/2, mae_values, width, label='MAE (%)', alpha=0.8, color='skyblue')
        axes[1, 1].bar(x + width/2, mape_values, width, label='MAPE (%)', alpha=0.8, color='lightcoral')
        axes[1, 1].axhline(y=self.target_mae, color='blue', linestyle='--', alpha=0.7, label=f'MAE Target ({self.target_mae}%)')
        axes[1, 1].axhline(y=self.target_mape, color='red', linestyle='--', alpha=0.7, label=f'MAPE Target ({self.target_mape}%)')
        axes[1, 1].set_title('Performance Metrics Comparison')
        axes[1, 1].set_xlabel('Dataset')
        axes[1, 1].set_ylabel('Error (%)')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(datasets_names)
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('lstm_model_evaluation.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Evaluation plots saved as 'lstm_model_evaluation.png'")

    def save_model_and_results(self):
        """Save trained model and evaluation results"""
        print(f"\n💾 Saving model and results...")

        # Create models directory
        os.makedirs('models', exist_ok=True)

        # Save the trained model
        self.model.save('models/lstm_forecasting_model.h5')

        # Save scalers
        with open('models/lstm_scalers.pkl', 'wb') as f:
            pickle.dump({
                'target_scaler': self.scaler,
                'feature_scaler': self.feature_scaler,
                'sequence_length': self.sequence_length
            }, f)

        # Save evaluation results
        with open('models/lstm_evaluation_results.json', 'w') as f:
            json.dump(self.evaluation_results, f, indent=2, default=str)

        # Save comprehensive results as pickle
        with open('models/lstm_comprehensive_results.pkl', 'wb') as f:
            pickle.dump({
                'model_path': 'models/lstm_forecasting_model.h5',
                'scalers': {
                    'target_scaler': self.scaler,
                    'feature_scaler': self.feature_scaler
                },
                'evaluation_results': self.evaluation_results,
                'training_history': self.training_history.history if self.training_history else None,
                'model_config': {
                    'sequence_length': self.sequence_length,
                    'target_mae': self.target_mae,
                    'target_mape': self.target_mape
                }
            }, f)

        print("✅ Model and results saved successfully:")
        print("   - models/lstm_forecasting_model.h5")
        print("   - models/lstm_scalers.pkl")
        print("   - models/lstm_evaluation_results.json")
        print("   - models/lstm_comprehensive_results.pkl")


def main():
    """Main execution function for LSTM forecasting"""
    print("🚀 LSTM TIME SERIES FORECASTING")
    print("="*60)
    print("Target Performance: MAE <5%, MAPE <5%")
    print("="*60)

    # Initialize forecaster
    forecaster = LSTMForecaster(sequence_length=30, target_mae=5.0, target_mape=5.0)

    try:
        # Step 1: Load and prepare data
        data = forecaster.load_and_prepare_data()

        # Step 2: Prepare sequences
        X, y, dates = forecaster.prepare_sequences(data)

        # Step 3: Split data
        train_data, val_data, test_data = forecaster.split_data(X, y, dates)

        # Step 4: Train model
        print(f"\n🎯 Starting LSTM training...")
        history = forecaster.train_model(train_data, val_data, epochs=200, batch_size=32)

        # Step 5: Evaluate model
        results = forecaster.evaluate_model(train_data, val_data, test_data)

        # Step 6: Save model and results
        forecaster.save_model_and_results()

        # Final summary
        test_metrics = results['test_metrics']
        mae_achieved = test_metrics['mae_percent'] < 5.0
        mape_achieved = test_metrics['mape'] < 5.0

        print(f"\n🏁 LSTM FORECASTING COMPLETED")
        print("="*60)
        print(f"Final Test Performance:")
        print(f"  MAE: {test_metrics['mae_percent']:.2f}% (Target: <5%) - {'✅' if mae_achieved else '❌'}")
        print(f"  MAPE: {test_metrics['mape']:.2f}% (Target: <5%) - {'✅' if mape_achieved else '❌'}")
        print(f"  RMSE: {test_metrics['rmse_percent']:.2f}%")
        print(f"  R²: {test_metrics['r2']:.4f}")

        if mae_achieved and mape_achieved:
            print(f"\n🎉 SUCCESS: Both performance targets achieved!")
            grade = "A+"
        elif mae_achieved or mape_achieved:
            print(f"\n⚠️  PARTIAL SUCCESS: One target achieved")
            grade = "B"
        else:
            print(f"\n❌ TARGETS NOT MET: Model needs improvement")
            grade = "C"

        print(f"Overall Grade: {grade}")

        return forecaster, results

    except Exception as e:
        print(f"❌ Error during LSTM training: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    forecaster, results = main()
