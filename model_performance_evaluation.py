#!/usr/bin/env python3
"""
Comprehensive ARIMA Model Performance Evaluation
Analyzes MAE, MAPE, RMSE against specific targets and provides scoring
Targets: MAE <5%, MAPE <5%, RMSE <10%
"""

import pandas as pd
import numpy as np
import json
import pickle
import matplotlib.pyplot as plt
from datetime import datetime
import os
import warnings
warnings.filterwarnings('ignore')

class ModelPerformanceEvaluator:
    def __init__(self):
        self.targets = {
            'mae_percent': 5.0,    # MAE < 5%
            'mape_percent': 5.0,   # MAPE < 5%
            'rmse_percent': 10.0   # RMSE < 10%
        }
        self.scoring_weights = {
            'mae': 0.35,
            'mape': 0.35,
            'rmse': 0.30
        }
        
    def load_model_results(self, results_file):
        """Load model results from JSON file"""
        try:
            with open(results_file, 'r') as f:
                results = json.load(f)
            print(f"✅ Loaded results from: {results_file}")
            return results
        except Exception as e:
            print(f"❌ Error loading {results_file}: {str(e)}")
            return None
    
    def calculate_percentage_metrics(self, results, original_data_stats):
        """Calculate percentage-based metrics"""
        performance = results['performance_metrics']

        # Get mean of original data for percentage calculations
        mean_original = original_data_stats['mean']

        # The MAE and RMSE are calculated on processed (log-transformed, differenced) scale
        # We need to convert them to original scale for meaningful percentage calculations

        # For log-transformed data, we need to convert back to original scale
        # This is an approximation since we're dealing with differenced log data

        # Get the processed MAE and RMSE
        mae_processed = performance['mae']
        rmse_processed = performance['rmse']

        # Convert to approximate original scale percentages
        # Since the data is log-transformed and differenced, these are relative changes
        # We'll use the processed values as percentage changes directly
        mae_percent = mae_processed * 100  # Convert to percentage
        rmse_percent = rmse_processed * 100  # Convert to percentage
        mape_percent = performance['mape']  # Already in percentage

        return {
            'mae_absolute': mae_processed,
            'mae_percent': mae_percent,
            'rmse_absolute': rmse_processed,
            'rmse_percent': rmse_percent,
            'mape_percent': mape_percent,
            'note': 'MAE and RMSE percentages calculated from processed (log-transformed, differenced) scale'
        }
    
    def evaluate_against_targets(self, metrics):
        """Evaluate metrics against targets"""
        evaluation = {}
        
        # MAE evaluation
        mae_meets_target = metrics['mae_percent'] < self.targets['mae_percent']
        mae_score = max(0, 100 - (metrics['mae_percent'] / self.targets['mae_percent']) * 100)
        if mae_meets_target:
            mae_score = 100
        
        evaluation['mae'] = {
            'value': metrics['mae_percent'],
            'target': self.targets['mae_percent'],
            'meets_target': mae_meets_target,
            'score': mae_score,
            'status': '✅ PASSED' if mae_meets_target else '❌ FAILED'
        }
        
        # MAPE evaluation
        mape_meets_target = metrics['mape_percent'] < self.targets['mape_percent']
        mape_score = max(0, 100 - (metrics['mape_percent'] / self.targets['mape_percent']) * 100)
        if mape_meets_target:
            mape_score = 100
        
        evaluation['mape'] = {
            'value': metrics['mape_percent'],
            'target': self.targets['mape_percent'],
            'meets_target': mape_meets_target,
            'score': mape_score,
            'status': '✅ PASSED' if mape_meets_target else '❌ FAILED'
        }
        
        # RMSE evaluation
        rmse_meets_target = metrics['rmse_percent'] < self.targets['rmse_percent']
        rmse_score = max(0, 100 - (metrics['rmse_percent'] / self.targets['rmse_percent']) * 100)
        if rmse_meets_target:
            rmse_score = 100
        
        evaluation['rmse'] = {
            'value': metrics['rmse_percent'],
            'target': self.targets['rmse_percent'],
            'meets_target': rmse_meets_target,
            'score': rmse_score,
            'status': '✅ PASSED' if rmse_meets_target else '❌ FAILED'
        }
        
        return evaluation
    
    def calculate_final_score(self, evaluation):
        """Calculate weighted final score"""
        mae_score = evaluation['mae']['score']
        mape_score = evaluation['mape']['score']
        rmse_score = evaluation['rmse']['score']
        
        final_score = (
            mae_score * self.scoring_weights['mae'] +
            mape_score * self.scoring_weights['mape'] +
            rmse_score * self.scoring_weights['rmse']
        )
        
        # Determine grade
        if final_score >= 90:
            grade = 'A+'
        elif final_score >= 80:
            grade = 'A'
        elif final_score >= 70:
            grade = 'B+'
        elif final_score >= 60:
            grade = 'B'
        elif final_score >= 50:
            grade = 'C+'
        elif final_score >= 40:
            grade = 'C'
        else:
            grade = 'F'
        
        return {
            'final_score': final_score,
            'grade': grade,
            'component_scores': {
                'mae_score': mae_score,
                'mape_score': mape_score,
                'rmse_score': rmse_score
            },
            'weights_used': self.scoring_weights
        }
    
    def get_original_data_stats(self):
        """Get statistics from original data for percentage calculations"""
        try:
            data = pd.read_csv('preprocessed_timeseries.csv')
            original_revenue = data['DailyRevenue']
            
            stats = {
                'mean': original_revenue.mean(),
                'std': original_revenue.std(),
                'min': original_revenue.min(),
                'max': original_revenue.max(),
                'median': original_revenue.median()
            }
            
            print(f"📊 Original Data Statistics:")
            print(f"   Mean Daily Revenue: ${stats['mean']:,.2f}")
            print(f"   Std Daily Revenue: ${stats['std']:,.2f}")
            print(f"   Min Daily Revenue: ${stats['min']:,.2f}")
            print(f"   Max Daily Revenue: ${stats['max']:,.2f}")
            
            return stats
            
        except Exception as e:
            print(f"❌ Error getting original data stats: {str(e)}")
            return None
    
    def create_performance_visualization(self, evaluations, output_file='model_performance_comparison.png'):
        """Create comprehensive performance visualization"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        models = list(evaluations.keys())
        
        # 1. Metric Values vs Targets
        metrics = ['mae', 'mape', 'rmse']
        x_pos = np.arange(len(models))
        width = 0.25
        
        for i, metric in enumerate(metrics):
            values = [evaluations[model]['evaluation'][metric]['value'] for model in models]
            target = evaluations[models[0]]['evaluation'][metric]['target']
            
            ax1.bar(x_pos + i*width, values, width, 
                   label=f'{metric.upper()} Values', alpha=0.8)
            ax1.axhline(y=target, color='red', linestyle='--', alpha=0.7,
                       label=f'{metric.upper()} Target ({target}%)' if i == 0 else "")
        
        ax1.set_xlabel('Models')
        ax1.set_ylabel('Percentage (%)')
        ax1.set_title('Model Performance vs Targets')
        ax1.set_xticks(x_pos + width)
        ax1.set_xticklabels(models)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. Individual Scores
        mae_scores = [evaluations[model]['evaluation']['mae']['score'] for model in models]
        mape_scores = [evaluations[model]['evaluation']['mape']['score'] for model in models]
        rmse_scores = [evaluations[model]['evaluation']['rmse']['score'] for model in models]
        
        ax2.bar(x_pos - width, mae_scores, width, label='MAE Score', alpha=0.8)
        ax2.bar(x_pos, mape_scores, width, label='MAPE Score', alpha=0.8)
        ax2.bar(x_pos + width, rmse_scores, width, label='RMSE Score', alpha=0.8)
        
        ax2.set_xlabel('Models')
        ax2.set_ylabel('Score (0-100)')
        ax2.set_title('Individual Metric Scores')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(models)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 100)
        
        # 3. Final Scores
        final_scores = [evaluations[model]['final_scoring']['final_score'] for model in models]
        grades = [evaluations[model]['final_scoring']['grade'] for model in models]
        
        bars = ax3.bar(models, final_scores, alpha=0.8, 
                      color=['green' if score >= 70 else 'orange' if score >= 50 else 'red' 
                             for score in final_scores])
        
        # Add grade labels on bars
        for bar, grade in zip(bars, grades):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'Grade: {grade}', ha='center', va='bottom', fontweight='bold')
        
        ax3.set_xlabel('Models')
        ax3.set_ylabel('Final Score (0-100)')
        ax3.set_title('Final Model Scores with Grades')
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 100)
        
        # 4. Target Achievement Summary
        target_data = []
        for model in models:
            eval_data = evaluations[model]['evaluation']
            target_data.append([
                1 if eval_data['mae']['meets_target'] else 0,
                1 if eval_data['mape']['meets_target'] else 0,
                1 if eval_data['rmse']['meets_target'] else 0
            ])
        
        target_df = pd.DataFrame(target_data, columns=['MAE', 'MAPE', 'RMSE'], index=models)

        # Create heatmap using matplotlib
        im = ax4.imshow(target_df.values, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)

        # Add text annotations
        for i in range(len(models)):
            for j in range(len(['MAE', 'MAPE', 'RMSE'])):
                text = ax4.text(j, i, target_df.iloc[i, j],
                              ha="center", va="center", color="black", fontweight='bold')

        ax4.set_xticks(range(len(['MAE', 'MAPE', 'RMSE'])))
        ax4.set_yticks(range(len(models)))
        ax4.set_xticklabels(['MAE', 'MAPE', 'RMSE'])
        ax4.set_yticklabels(models)
        ax4.set_title('Target Achievement Heatmap\n(1=Target Met, 0=Target Not Met)')
        ax4.set_xlabel('Metrics')
        ax4.set_ylabel('Models')

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax4)
        cbar.set_label('Target Met (1=Yes, 0=No)')
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ Performance visualization saved: {output_file}")
        return output_file

    def print_detailed_report(self, model_name, metrics, evaluation, final_scoring):
        """Print detailed performance report"""
        print(f"\n{'='*80}")
        print(f"DETAILED PERFORMANCE REPORT - {model_name.upper()}")
        print(f"{'='*80}")

        print(f"\n📊 PERFORMANCE METRICS:")
        print(f"   MAE (Absolute): {metrics['mae_absolute']:.6f}")
        print(f"   MAE (Percentage): {metrics['mae_percent']:.2f}% {evaluation['mae']['status']}")
        print(f"   MAPE (Percentage): {metrics['mape_percent']:.2f}% {evaluation['mape']['status']}")
        print(f"   RMSE (Absolute): {metrics['rmse_absolute']:.6f}")
        print(f"   RMSE (Percentage): {metrics['rmse_percent']:.2f}% {evaluation['rmse']['status']}")

        print(f"\n🎯 TARGET ANALYSIS:")
        print(f"   MAE Target: <{self.targets['mae_percent']}% | Actual: {metrics['mae_percent']:.2f}% | Score: {evaluation['mae']['score']:.1f}/100")
        print(f"   MAPE Target: <{self.targets['mape_percent']}% | Actual: {metrics['mape_percent']:.2f}% | Score: {evaluation['mape']['score']:.1f}/100")
        print(f"   RMSE Target: <{self.targets['rmse_percent']}% | Actual: {metrics['rmse_percent']:.2f}% | Score: {evaluation['rmse']['score']:.1f}/100")

        print(f"\n🏆 FINAL SCORING:")
        print(f"   Component Scores:")
        print(f"     - MAE Score: {final_scoring['component_scores']['mae_score']:.1f}/100 (Weight: {self.scoring_weights['mae']*100:.0f}%)")
        print(f"     - MAPE Score: {final_scoring['component_scores']['mape_score']:.1f}/100 (Weight: {self.scoring_weights['mape']*100:.0f}%)")
        print(f"     - RMSE Score: {final_scoring['component_scores']['rmse_score']:.1f}/100 (Weight: {self.scoring_weights['rmse']*100:.0f}%)")
        print(f"   Final Score: {final_scoring['final_score']:.1f}/100")
        print(f"   Grade: {final_scoring['grade']}")

        # Performance interpretation
        if final_scoring['final_score'] >= 70:
            interpretation = "🟢 EXCELLENT - Model meets production standards"
        elif final_scoring['final_score'] >= 50:
            interpretation = "🟡 ACCEPTABLE - Model needs improvement but usable"
        else:
            interpretation = "🔴 POOR - Model requires significant improvement"

        print(f"   Interpretation: {interpretation}")

    def save_evaluation_results(self, evaluations, filename='model_evaluation_comprehensive.pkl'):
        """Save comprehensive evaluation results"""
        try:
            # Create models directory if it doesn't exist
            os.makedirs('models', exist_ok=True)

            # Prepare comprehensive results
            comprehensive_results = {
                'evaluation_timestamp': datetime.now().isoformat(),
                'targets': self.targets,
                'scoring_weights': self.scoring_weights,
                'model_evaluations': evaluations,
                'summary': {
                    'total_models_evaluated': len(evaluations),
                    'models_meeting_all_targets': sum(1 for model_eval in evaluations.values()
                                                    if all(metric['meets_target'] for metric in model_eval['evaluation'].values())),
                    'best_model': max(evaluations.keys(),
                                    key=lambda x: evaluations[x]['final_scoring']['final_score']),
                    'average_score': np.mean([eval_data['final_scoring']['final_score']
                                            for eval_data in evaluations.values()])
                }
            }

            # Save to models directory
            filepath = os.path.join('models', filename)
            with open(filepath, 'wb') as f:
                pickle.dump(comprehensive_results, f)

            print(f"\n✅ Comprehensive evaluation results saved: {filepath}")

            # Also save as JSON for readability
            json_filename = filename.replace('.pkl', '.json')
            json_filepath = os.path.join('models', json_filename)

            # Convert numpy types for JSON serialization
            json_results = json.loads(json.dumps(comprehensive_results, default=str))

            with open(json_filepath, 'w') as f:
                json.dump(json_results, f, indent=2)

            print(f"✅ JSON version saved: {json_filepath}")

            return filepath

        except Exception as e:
            print(f"❌ Error saving evaluation results: {str(e)}")
            return None

def main():
    """Main evaluation pipeline"""
    print("="*100)
    print("COMPREHENSIVE ARIMA MODEL PERFORMANCE EVALUATION")
    print("="*100)
    print("Targets: MAE <5%, MAPE <5%, RMSE <10%")
    print("="*100)

    evaluator = ModelPerformanceEvaluator()

    # Get original data statistics
    original_stats = evaluator.get_original_data_stats()
    if original_stats is None:
        return None

    # Models to evaluate
    model_files = {
        'ARIMA(1,1,1)': 'arima_1_1_1_results.json',
        'ARIMA(1,1,2)': 'arima_1_1_2_results.json'
    }

    evaluations = {}

    # Evaluate each model
    for model_name, results_file in model_files.items():
        print(f"\n{'='*60}")
        print(f"EVALUATING {model_name}")
        print(f"{'='*60}")

        # Load results
        results = evaluator.load_model_results(results_file)
        if results is None:
            continue

        # Calculate percentage metrics
        metrics = evaluator.calculate_percentage_metrics(results, original_stats)

        # Evaluate against targets
        evaluation = evaluator.evaluate_against_targets(metrics)

        # Calculate final score
        final_scoring = evaluator.calculate_final_score(evaluation)

        # Store evaluation
        evaluations[model_name] = {
            'metrics': metrics,
            'evaluation': evaluation,
            'final_scoring': final_scoring,
            'model_info': results.get('model_info', {})
        }

        # Print detailed report
        evaluator.print_detailed_report(model_name, metrics, evaluation, final_scoring)

    if not evaluations:
        print("❌ No models could be evaluated")
        return None

    # Create performance visualization
    print(f"\n{'='*80}")
    print("CREATING PERFORMANCE VISUALIZATION")
    print(f"{'='*80}")

    viz_file = evaluator.create_performance_visualization(evaluations)

    # Save comprehensive results
    print(f"\n{'='*80}")
    print("SAVING COMPREHENSIVE RESULTS")
    print(f"{'='*80}")

    results_file = evaluator.save_evaluation_results(evaluations)

    # Final summary
    print(f"\n{'='*100}")
    print("EVALUATION SUMMARY")
    print(f"{'='*100}")

    best_model = max(evaluations.keys(),
                    key=lambda x: evaluations[x]['final_scoring']['final_score'])
    best_score = evaluations[best_model]['final_scoring']['final_score']
    best_grade = evaluations[best_model]['final_scoring']['grade']

    print(f"🏆 Best Performing Model: {best_model}")
    print(f"📊 Best Score: {best_score:.1f}/100 (Grade: {best_grade})")

    # Target achievement summary
    for model_name, eval_data in evaluations.items():
        targets_met = sum(1 for metric in eval_data['evaluation'].values() if metric['meets_target'])
        print(f"   {model_name}: {targets_met}/3 targets met")

    print(f"\n✅ Evaluation completed successfully!")
    print(f"📁 Results saved in models/ directory")
    print(f"📊 Visualization: {viz_file}")

    return evaluations

if __name__ == "__main__":
    results = main()
