#!/usr/bin/env python3
"""
Improved ARIMA Model Training with Better Preprocessing and Model Persistence
"""

import pandas as pd
import numpy as np
import pickle
import json
from datetime import datetime
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import adfuller
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def load_and_analyze_data():
    """Load and analyze the original data"""
    print("Loading and analyzing original data...")
    
    # Load original daily revenue data
    data = pd.read_csv('daily_revenue_timeseries.csv')
    data['Date'] = pd.to_datetime(data['Date'])
    data = data.sort_values('Date').reset_index(drop=True)
    
    print(f"✅ Data loaded: {len(data)} observations")
    print(f"   Date range: {data['Date'].min()} to {data['Date'].max()}")
    print(f"   Revenue range: ${data['DailyRevenue'].min():,.2f} to ${data['DailyRevenue'].max():,.2f}")
    print(f"   Average daily revenue: ${data['DailyRevenue'].mean():,.2f}")
    
    return data

def improved_preprocessing(data):
    """Apply improved preprocessing that preserves interpretability"""
    print("\nApplying improved preprocessing...")
    
    revenue_series = data['DailyRevenue'].copy()
    
    # Check for stationarity
    adf_result = adfuller(revenue_series)
    print(f"ADF Test p-value (original): {adf_result[1]:.6f}")
    
    # Strategy 1: Try simple differencing first
    diff_series = revenue_series.diff().dropna()
    adf_diff = adfuller(diff_series)
    print(f"ADF Test p-value (differenced): {adf_diff[1]:.6f}")
    
    # Strategy 2: Try percentage change (more interpretable than log)
    pct_series = revenue_series.pct_change().dropna()
    adf_pct = adfuller(pct_series)
    print(f"ADF Test p-value (pct_change): {adf_pct[1]:.6f}")
    
    # Choose the best transformation
    if adf_diff[1] < 0.05:
        print("✅ Using simple differencing (stationary)")
        processed_series = diff_series
        transformation = 'differencing'
        base_value = revenue_series.iloc[0]  # Store first value for inverse transform
    elif adf_pct[1] < 0.05:
        print("✅ Using percentage change (stationary)")
        processed_series = pct_series
        transformation = 'pct_change'
        base_series = revenue_series.copy()  # Store original series for inverse transform
    else:
        print("⚠️ Using differencing (best available)")
        processed_series = diff_series
        transformation = 'differencing'
        base_value = revenue_series.iloc[0]
    
    # Prepare final dataset
    if transformation == 'differencing':
        final_data = data.iloc[1:].copy()  # Skip first row due to differencing
        final_data['ProcessedRevenue'] = processed_series.values
        preprocessing_params = {
            'transformation': transformation,
            'base_value': float(base_value),
            'original_length': len(data),
            'processed_length': len(final_data)
        }
    else:  # pct_change
        final_data = data.iloc[1:].copy()  # Skip first row due to pct_change
        final_data['ProcessedRevenue'] = processed_series.values
        preprocessing_params = {
            'transformation': transformation,
            'base_series': base_series.tolist(),
            'original_length': len(data),
            'processed_length': len(final_data)
        }
    
    print(f"✅ Preprocessing complete: {len(final_data)} observations")
    print(f"   Transformation: {transformation}")
    print(f"   Processed series range: {processed_series.min():.4f} to {processed_series.max():.4f}")
    
    return final_data, preprocessing_params

def inverse_transform_forecast(forecasts, preprocessing_params, last_values):
    """Apply inverse transformation to forecasts"""
    transformation = preprocessing_params['transformation']
    
    if transformation == 'differencing':
        # For differencing: forecast = last_value + predicted_difference
        base_value = last_values[-1]  # Last actual value
        original_forecasts = []
        current_value = base_value
        
        for diff in forecasts:
            current_value = current_value + diff
            original_forecasts.append(current_value)
        
        return np.array(original_forecasts)
    
    elif transformation == 'pct_change':
        # For percentage change: forecast = last_value * (1 + predicted_pct_change)
        base_value = last_values[-1]  # Last actual value
        original_forecasts = []
        current_value = base_value
        
        for pct in forecasts:
            current_value = current_value * (1 + pct)
            original_forecasts.append(current_value)
        
        return np.array(original_forecasts)
    
    return forecasts  # Fallback

def train_and_evaluate_models(data, preprocessing_params):
    """Train multiple ARIMA models and select the best one"""
    print("\nTraining and evaluating ARIMA models...")
    
    # Prepare train/test split
    n_test = max(30, int(len(data) * 0.2))  # At least 30 days for testing
    train_data = data.iloc[:-n_test].copy()
    test_data = data.iloc[-n_test:].copy()
    
    train_series = train_data['ProcessedRevenue']
    test_series = test_data['ProcessedRevenue']
    
    print(f"   Training data: {len(train_series)} observations")
    print(f"   Test data: {len(test_series)} observations")
    
    # Test different ARIMA orders
    orders_to_test = [
        (1, 0, 1), (2, 0, 1), (1, 0, 2), (2, 0, 2),
        (3, 0, 1), (1, 0, 3), (3, 0, 3),
        (0, 0, 1), (1, 0, 0), (2, 0, 0)
    ]
    
    best_model = None
    best_order = None
    best_aic = float('inf')
    best_performance = None
    results = []
    
    for order in orders_to_test:
        try:
            print(f"   Testing ARIMA{order}...")
            
            # Fit model
            model = ARIMA(train_series, order=order)
            fitted_model = model.fit()
            
            # Generate forecasts
            forecast_result = fitted_model.get_forecast(steps=len(test_series))
            forecasts_processed = forecast_result.predicted_mean.values
            
            # Apply inverse transformation
            last_train_values = train_data['DailyRevenue'].values
            forecasts_original = inverse_transform_forecast(
                forecasts_processed, preprocessing_params, last_train_values
            )
            
            # Calculate metrics on original scale
            test_original = test_data['DailyRevenue'].values
            mae = mean_absolute_error(test_original, forecasts_original)
            rmse = np.sqrt(mean_squared_error(test_original, forecasts_original))
            mape = np.mean(np.abs((test_original - forecasts_original) / test_original)) * 100
            
            # Store results
            result = {
                'order': order,
                'aic': fitted_model.aic,
                'bic': fitted_model.bic,
                'mae': mae,
                'rmse': rmse,
                'mape': mape,
                'mae_pct': (mae / np.mean(test_original)) * 100,
                'rmse_pct': (rmse / np.mean(test_original)) * 100
            }
            results.append(result)
            
            print(f"      AIC: {fitted_model.aic:.2f}, MAPE: {mape:.2f}%")
            
            # Check if this is the best model (by AIC, but also consider MAPE)
            if fitted_model.aic < best_aic and mape < 50:  # Reasonable MAPE threshold
                best_aic = fitted_model.aic
                best_model = fitted_model
                best_order = order
                best_performance = {
                    'forecasts_processed': forecasts_processed,
                    'forecasts_original': forecasts_original,
                    'test_original': test_original,
                    'metrics': result
                }
            
        except Exception as e:
            print(f"      ❌ Failed: {str(e)}")
            continue
    
    if best_model is None:
        print("❌ No suitable model found!")
        return None, None, None
    
    print(f"\n🏆 Best model: ARIMA{best_order}")
    print(f"   AIC: {best_performance['metrics']['aic']:.2f}")
    print(f"   MAPE: {best_performance['metrics']['mape']:.2f}%")
    print(f"   MAE: ${best_performance['metrics']['mae']:,.2f}")
    print(f"   RMSE: ${best_performance['metrics']['rmse']:,.2f}")
    
    return best_model, best_order, best_performance, results

def save_model_and_results(model, order, performance, results, preprocessing_params, data):
    """Save the trained model and all results"""
    print("\nSaving model and results...")
    
    # Create models directory if it doesn't exist
    import os
    os.makedirs('models', exist_ok=True)
    
    # Save the fitted model as pickle
    model_filename = f'models/arima_{order[0]}_{order[1]}_{order[2]}_model.pkl'
    with open(model_filename, 'wb') as f:
        pickle.dump(model, f)
    print(f"✅ Model saved: {model_filename}")
    
    # Save preprocessing parameters
    preprocessing_filename = 'models/preprocessing_params.json'
    with open(preprocessing_filename, 'w') as f:
        json.dump(preprocessing_params, f, indent=2)
    print(f"✅ Preprocessing params saved: {preprocessing_filename}")
    
    # Save model metadata
    metadata = {
        'model_info': {
            'order': order,
            'aic': model.aic,
            'bic': model.bic,
            'log_likelihood': model.llf,
            'training_date': datetime.now().isoformat(),
            'data_points': len(data)
        },
        'performance': performance['metrics'],
        'preprocessing': preprocessing_params,
        'model_file': model_filename,
        'preprocessing_file': preprocessing_filename
    }
    
    metadata_filename = 'models/model_metadata.json'
    with open(metadata_filename, 'w') as f:
        json.dump(metadata, f, indent=2)
    print(f"✅ Model metadata saved: {metadata_filename}")
    
    # Save all results comparison
    results_df = pd.DataFrame(results)
    results_df.to_csv('models/model_comparison.csv', index=False)
    print(f"✅ Model comparison saved: models/model_comparison.csv")
    
    return model_filename, metadata_filename

def main():
    """Main execution function"""
    print("="*60)
    print("IMPROVED ARIMA MODEL TRAINING")
    print("="*60)
    
    # Load and analyze data
    data = load_and_analyze_data()
    
    # Apply improved preprocessing
    processed_data, preprocessing_params = improved_preprocessing(data)
    
    # Train and evaluate models
    best_model, best_order, best_performance, all_results = train_and_evaluate_models(
        processed_data, preprocessing_params
    )
    
    if best_model is None:
        print("❌ Model training failed!")
        return
    
    # Save model and results
    model_file, metadata_file = save_model_and_results(
        best_model, best_order, best_performance, all_results, 
        preprocessing_params, processed_data
    )
    
    print("\n" + "="*60)
    print("✅ IMPROVED MODEL TRAINING COMPLETE!")
    print("="*60)
    print(f"Best model: ARIMA{best_order}")
    print(f"MAPE: {best_performance['metrics']['mape']:.2f}%")
    print(f"Model file: {model_file}")
    print(f"Metadata file: {metadata_file}")
    print("="*60)

if __name__ == "__main__":
    main()
