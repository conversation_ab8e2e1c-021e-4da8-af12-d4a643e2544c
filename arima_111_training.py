#!/usr/bin/env python3
"""
ARIMA (1,1,1) Model Training with Auto ARIMA Functionality
This script trains ARIMA models with (1,1,1) parameters and implements
a simple auto ARIMA approach for parameter optimization.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import json
import warnings
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.tsa.stattools import adfuller
from sklearn.metrics import mean_absolute_error, mean_squared_error
from itertools import product
import pickle
import os
from datetime import datetime

warnings.filterwarnings('ignore')

def load_preprocessed_data():
    """Load preprocessed time series data"""
    print("Loading preprocessed data...")
    
    try:
        data = pd.read_csv('preprocessed_timeseries.csv')
        data['Date'] = pd.to_datetime(data['Date'])
        data = data.sort_values('Date').reset_index(drop=True)
        
        # Load preprocessing parameters
        with open('preprocessing_parameters.json', 'r') as f:
            preprocessing_params = json.load(f)
        
        print(f"✅ Data loaded: {len(data)} observations")
        print(f"   Date range: {data['Date'].min()} to {data['Date'].max()}")
        print(f"   Preprocessing: {preprocessing_params}")
        
        return data, preprocessing_params
        
    except Exception as e:
        print(f"❌ Error loading data: {str(e)}")
        return None, None

def split_train_test(data, test_ratio=0.2):
    """Split data into train and test sets"""
    n_test = int(len(data) * test_ratio)
    train_data = data.iloc[:-n_test].copy()
    test_data = data.iloc[-n_test:].copy()
    
    print(f"\nData split:")
    print(f"   Training: {len(train_data)} observations")
    print(f"   Testing: {len(test_data)} observations")
    
    return train_data, test_data

def fit_arima_111(train_series):
    """Fit ARIMA(1,1,1) model"""
    print(f"\n{'='*50}")
    print("FITTING ARIMA(1,1,1) MODEL")
    print(f"{'='*50}")
    
    try:
        model = ARIMA(train_series, order=(1, 1, 1))
        fitted_model = model.fit()
        
        print(f"✅ ARIMA(1,1,1) fitted successfully")
        print(f"   AIC: {fitted_model.aic:.2f}")
        print(f"   BIC: {fitted_model.bic:.2f}")
        print(f"   Log-likelihood: {fitted_model.llf:.2f}")
        
        # Model parameters
        print(f"\nModel Parameters:")
        ar_coef = fitted_model.params.get('ar.L1', 'N/A')
        ma_coef = fitted_model.params.get('ma.L1', 'N/A')
        const_coef = fitted_model.params.get('const', 'N/A')

        print(f"   AR(1) coefficient: {ar_coef:.4f}" if ar_coef != 'N/A' else "   AR(1) coefficient: N/A")
        print(f"   MA(1) coefficient: {ma_coef:.4f}" if ma_coef != 'N/A' else "   MA(1) coefficient: N/A")
        print(f"   Constant: {const_coef:.4f}" if const_coef != 'N/A' else "   Constant: N/A")
        
        return fitted_model
        
    except Exception as e:
        print(f"❌ Failed to fit ARIMA(1,1,1): {str(e)}")
        return None

def simple_auto_arima(train_series, max_p=3, max_q=3, d=1):
    """Simple auto ARIMA implementation"""
    print(f"\n{'='*50}")
    print("SIMPLE AUTO ARIMA OPTIMIZATION")
    print(f"{'='*50}")
    
    print(f"Search space: p(0-{max_p}), d={d}, q(0-{max_q})")
    
    best_aic = float('inf')
    best_order = None
    best_model = None
    results = []
    
    # Generate parameter combinations
    p_values = range(0, max_p + 1)
    q_values = range(0, max_q + 1)
    
    total_combinations = len(p_values) * len(q_values)
    print(f"Testing {total_combinations} parameter combinations...")
    
    for i, (p, q) in enumerate(product(p_values, q_values)):
        try:
            # Skip invalid combinations
            if p == 0 and q == 0:
                continue
            
            order = (p, d, q)
            model = ARIMA(train_series, order=order)
            fitted_model = model.fit()
            
            aic = fitted_model.aic
            bic = fitted_model.bic
            
            results.append({
                'order': order,
                'aic': aic,
                'bic': bic,
                'converged': fitted_model.mle_retvals.get('converged', True) if hasattr(fitted_model, 'mle_retvals') else True
            })
            
            print(f"   ARIMA{order}: AIC={aic:.2f}, BIC={bic:.2f}")
            
            if aic < best_aic:
                best_aic = aic
                best_order = order
                best_model = fitted_model
            
        except Exception as e:
            print(f"   ARIMA({p},{d},{q}): Failed - {str(e)}")
            continue
    
    if best_model is not None:
        print(f"\n🏆 Best model: ARIMA{best_order}")
        print(f"📊 AIC: {best_aic:.2f}")
        print(f"📊 BIC: {best_model.bic:.2f}")
        
        return best_model, best_order, results
    else:
        print(f"❌ Auto ARIMA failed - no valid models found")
        return None, None, []

def evaluate_model(fitted_model, test_series, order, preprocessing_params, train_data):
    """Evaluate ARIMA model performance"""
    print(f"\n{'='*50}")
    print(f"EVALUATING ARIMA{order} MODEL")
    print(f"{'='*50}")
    
    try:
        # Generate forecasts
        forecast_result = fitted_model.get_forecast(steps=len(test_series))
        forecasts_processed = forecast_result.predicted_mean.values
        conf_int_processed = forecast_result.conf_int().values
        
        # Apply inverse transformation to get original scale
        last_train_values = train_data['DailyRevenue'].values
        forecasts_original = inverse_transform_forecast(
            forecasts_processed, preprocessing_params, last_train_values
        )
        
        # Calculate metrics on processed scale
        mae = mean_absolute_error(test_series, forecasts_processed)
        rmse = np.sqrt(mean_squared_error(test_series, forecasts_processed))
        mape = np.mean(np.abs((test_series - forecasts_processed) / test_series)) * 100
        
        # Performance thresholds
        mae_threshold = 0.5
        rmse_threshold = 0.7
        mape_threshold = 15.0
        
        meets_mae_target = mae <= mae_threshold
        meets_rmse_target = rmse <= rmse_threshold
        meets_mape_target = mape <= mape_threshold
        
        print(f"📊 Model Performance:")
        print(f"   MAE: {mae:.4f} {'✅' if meets_mae_target else '❌'} (target: ≤{mae_threshold})")
        print(f"   RMSE: {rmse:.4f} {'✅' if meets_rmse_target else '❌'} (target: ≤{rmse_threshold})")
        print(f"   MAPE: {mape:.2f}% {'✅' if meets_mape_target else '❌'} (target: ≤{mape_threshold}%)")
        
        overall_pass = meets_mae_target and meets_rmse_target and meets_mape_target
        print(f"🎯 Overall Performance: {'✅ PASSED' if overall_pass else '❌ NEEDS IMPROVEMENT'}")
        
        return {
            'forecasts': forecasts_processed,
            'forecasts_original': forecasts_original,
            'confidence_intervals': conf_int_processed,
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'meets_targets': overall_pass,
            'order': order
        }
        
    except Exception as e:
        print(f"❌ Error evaluating model: {str(e)}")
        return None

def inverse_transform_forecast(forecasts, preprocessing_params, last_train_values):
    """Apply inverse transformation to forecasts"""
    try:
        # This is a simplified inverse transformation
        # In practice, you'd need to properly handle differencing and log transformation
        if preprocessing_params.get('log_transformed', False):
            return np.exp(forecasts)
        return forecasts
    except Exception as e:
        print(f"⚠️ Warning: Inverse transformation failed: {str(e)}")
        return forecasts

def perform_residual_analysis(fitted_model, order):
    """Perform residual analysis"""
    print(f"\n{'='*50}")
    print(f"RESIDUAL ANALYSIS - ARIMA{order}")
    print(f"{'='*50}")

    try:
        residuals = fitted_model.resid

        # Ljung-Box test for autocorrelation
        lb_stat, lb_pvalue = acorr_ljungbox(residuals, lags=10, return_df=False)

        print(f"📊 Residual Diagnostics:")
        print(f"   Mean: {np.mean(residuals):.6f}")
        print(f"   Std: {np.std(residuals):.6f}")

        # Handle lb_pvalue which might be an array
        if isinstance(lb_pvalue, (list, np.ndarray)):
            pvalue = lb_pvalue[-1] if len(lb_pvalue) > 0 else lb_pvalue
        else:
            pvalue = lb_pvalue

        print(f"   Ljung-Box p-value: {pvalue:.6f}")
        print(f"   Autocorrelation test: {'✅ PASSED' if pvalue > 0.05 else '❌ FAILED'}")

        return {
            'mean': np.mean(residuals),
            'std': np.std(residuals),
            'ljung_box_pvalue': lb_pvalue[-1],
            'autocorr_test_passed': lb_pvalue[-1] > 0.05
        }

    except Exception as e:
        print(f"❌ Error in residual analysis: {str(e)}")
        return None

def save_model_results(model, order, performance, residual_analysis, auto_arima_results=None):
    """Save model results to files"""
    print(f"\n{'='*50}")
    print("SAVING MODEL RESULTS")
    print(f"{'='*50}")

    try:
        # Create models directory if it doesn't exist
        os.makedirs('models', exist_ok=True)

        # Save the fitted model
        model_filename = f'models/arima_{order[0]}_{order[1]}_{order[2]}_model.pkl'
        with open(model_filename, 'wb') as f:
            pickle.dump(model, f)
        print(f"✅ Model saved: {model_filename}")

        # Save results summary
        results = {
            'model_order': order,
            'model_type': 'ARIMA',
            'training_timestamp': datetime.now().isoformat(),
            'performance_metrics': performance,
            'residual_analysis': residual_analysis,
            'model_info': {
                'aic': model.aic,
                'bic': model.bic,
                'log_likelihood': model.llf,
                'parameters': dict(model.params) if hasattr(model, 'params') else {}
            }
        }

        if auto_arima_results:
            results['auto_arima_search'] = auto_arima_results

        results_filename = f'arima_{order[0]}_{order[1]}_{order[2]}_results.json'
        with open(results_filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"✅ Results saved: {results_filename}")

        return results_filename

    except Exception as e:
        print(f"❌ Error saving results: {str(e)}")
        return None

def create_forecast_visualization(train_data, test_data, forecasts, conf_int, order):
    """Create forecast visualization"""
    print(f"\n{'='*50}")
    print("CREATING FORECAST VISUALIZATION")
    print(f"{'='*50}")

    try:
        plt.figure(figsize=(15, 8))

        # Plot training data
        plt.plot(train_data['Date'], train_data['ProcessedRevenue'],
                label='Training Data', color='blue', alpha=0.7)

        # Plot test data
        plt.plot(test_data['Date'], test_data['ProcessedRevenue'],
                label='Actual Test Data', color='green', linewidth=2)

        # Plot forecasts
        plt.plot(test_data['Date'], forecasts,
                label=f'ARIMA{order} Forecast', color='red', linewidth=2)

        # Plot confidence intervals
        plt.fill_between(test_data['Date'],
                        conf_int[:, 0], conf_int[:, 1],
                        color='red', alpha=0.2, label='95% Confidence Interval')

        plt.title(f'ARIMA{order} Forecast vs Actual', fontsize=16, fontweight='bold')
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Processed Revenue', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        # Save plot
        plot_filename = f'arima{order}_forecast_visualization.png'
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ Visualization saved: {plot_filename}")
        return plot_filename

    except Exception as e:
        print(f"❌ Error creating visualization: {str(e)}")
        return None

def compare_models(arima_111_performance, auto_arima_performance=None):
    """Compare model performances"""
    print(f"\n{'='*60}")
    print("MODEL COMPARISON")
    print(f"{'='*60}")

    print(f"ARIMA(1,1,1) Performance:")
    print(f"   MAE: {arima_111_performance['mae']:.4f}")
    print(f"   RMSE: {arima_111_performance['rmse']:.4f}")
    print(f"   MAPE: {arima_111_performance['mape']:.2f}%")
    print(f"   Meets Targets: {'✅' if arima_111_performance['meets_targets'] else '❌'}")

    if auto_arima_performance:
        print(f"\nAuto ARIMA{auto_arima_performance['order']} Performance:")
        print(f"   MAE: {auto_arima_performance['mae']:.4f}")
        print(f"   RMSE: {auto_arima_performance['rmse']:.4f}")
        print(f"   MAPE: {auto_arima_performance['mape']:.2f}%")
        print(f"   Meets Targets: {'✅' if auto_arima_performance['meets_targets'] else '❌'}")

        # Determine which is better
        arima_111_score = sum([
            arima_111_performance['mae'],
            arima_111_performance['rmse'],
            arima_111_performance['mape'] / 100
        ])

        auto_arima_score = sum([
            auto_arima_performance['mae'],
            auto_arima_performance['rmse'],
            auto_arima_performance['mape'] / 100
        ])

        if arima_111_score < auto_arima_score:
            print(f"\n🏆 Winner: ARIMA(1,1,1) - Better overall performance")
        else:
            print(f"\n🏆 Winner: Auto ARIMA{auto_arima_performance['order']} - Better overall performance")

def main():
    """Main training pipeline"""
    print("="*80)
    print("ARIMA (1,1,1) TRAINING WITH AUTO ARIMA FUNCTIONALITY")
    print("="*80)

    # Load data
    data, preprocessing_params = load_preprocessed_data()
    if data is None:
        return None

    # Split data
    train_data, test_data = split_train_test(data)
    train_series = train_data['ProcessedRevenue']
    test_series = test_data['ProcessedRevenue']

    # 1. Fit ARIMA(1,1,1) model
    arima_111_model = fit_arima_111(train_series)
    if arima_111_model is None:
        print("❌ ARIMA(1,1,1) training failed")
        return None

    # Evaluate ARIMA(1,1,1)
    arima_111_performance = evaluate_model(
        arima_111_model, test_series, (1, 1, 1), preprocessing_params, train_data
    )

    # Residual analysis for ARIMA(1,1,1)
    arima_111_residuals = perform_residual_analysis(arima_111_model, (1, 1, 1))

    # 2. Run simple auto ARIMA
    auto_arima_model, auto_arima_order, auto_arima_results = simple_auto_arima(train_series)

    auto_arima_performance = None
    auto_arima_residuals = None

    if auto_arima_model is not None:
        # Evaluate auto ARIMA model
        auto_arima_performance = evaluate_model(
            auto_arima_model, test_series, auto_arima_order, preprocessing_params, train_data
        )

        # Residual analysis for auto ARIMA
        auto_arima_residuals = perform_residual_analysis(auto_arima_model, auto_arima_order)

    # Compare models
    compare_models(arima_111_performance, auto_arima_performance)

    # Save results
    arima_111_results_file = save_model_results(
        arima_111_model, (1, 1, 1), arima_111_performance, arima_111_residuals
    )

    if auto_arima_model is not None:
        auto_arima_results_file = save_model_results(
            auto_arima_model, auto_arima_order, auto_arima_performance,
            auto_arima_residuals, auto_arima_results
        )

    # Create visualizations
    arima_111_plot = create_forecast_visualization(
        train_data, test_data, arima_111_performance['forecasts'],
        arima_111_performance['confidence_intervals'], (1, 1, 1)
    )

    if auto_arima_performance:
        auto_arima_plot = create_forecast_visualization(
            train_data, test_data, auto_arima_performance['forecasts'],
            auto_arima_performance['confidence_intervals'], auto_arima_order
        )

    # Final summary
    print(f"\n{'='*80}")
    print("TRAINING SUMMARY")
    print(f"{'='*80}")
    print(f"✅ ARIMA(1,1,1) model trained and evaluated")
    print(f"✅ Auto ARIMA optimization completed")
    print(f"✅ Results saved and visualizations created")

    return {
        'arima_111': {
            'model': arima_111_model,
            'performance': arima_111_performance,
            'residuals': arima_111_residuals
        },
        'auto_arima': {
            'model': auto_arima_model,
            'order': auto_arima_order,
            'performance': auto_arima_performance,
            'residuals': auto_arima_residuals
        } if auto_arima_model else None
    }

if __name__ == "__main__":
    results = main()
