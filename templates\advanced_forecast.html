{% extends "base.html" %}

{% block title %}Advanced Forecasting - Time Series Dashboard{% endblock %}

{% block extra_head %}
<style>
    .forecast-form-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .forecast-form-card .form-control,
    .forecast-form-card .form-select {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 10px;
    }
    
    .forecast-form-card .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }
    
    .forecast-form-card .form-control:focus,
    .forecast-form-card .form-select:focus {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        color: white;
    }
    
    .forecast-form-card .form-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
    }
    
    .forecast-form-card .form-select option {
        background: #333;
        color: white;
    }
    
    .results-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }
    
    .chart-container {
        height: 500px;
        padding: 1rem;
        background: #f8f9fa;
    }
    
    .forecast-summary {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
    
    .forecast-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .forecast-label {
        font-size: 0.9rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    
    .loading-content {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }
    
    .forecast-table {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .model-info-badge {
        background: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        margin: 0.25rem;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-5 mb-0">
                        <i class="fas fa-crystal-ball text-primary me-3"></i>
                        Advanced Forecasting
                    </h1>
                    <p class="text-muted mt-2">Generate predictions with custom time periods and model selection</p>
                </div>
                <div>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Forecast Configuration -->
    <div class="row">
        <div class="col-lg-4">
            <div class="forecast-form-card">
                <h5 class="mb-4">
                    <i class="fas fa-cog me-2"></i>
                    Forecast Configuration
                </h5>
                
                <form id="forecastForm">
                    <div class="mb-3">
                        <label for="modelSelect" class="form-label">Select Model</label>
                        <select class="form-select" id="modelSelect" name="model" required>
                            <option value="">Choose a model...</option>
                            {% for model in available_models %}
                            <option value="{{ model }}">{{ model }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="forecastDays" class="form-label">Forecast Period (Days)</label>
                        <select class="form-select" id="forecastDays" name="days" required>
                            <option value="7">7 days (1 week)</option>
                            <option value="14">14 days (2 weeks)</option>
                            <option value="30" selected>30 days (1 month)</option>
                            <option value="60">60 days (2 months)</option>
                            <option value="90">90 days (3 months)</option>
                            <option value="180">180 days (6 months)</option>
                            <option value="365">365 days (1 year)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="startDate" class="form-label">Historical Data Start Date (Optional)</label>
                        <input type="date" class="form-control" id="startDate" name="start_date" 
                               min="{{ historical_data_range.start }}" max="{{ historical_data_range.end }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="endDate" class="form-label">Historical Data End Date (Optional)</label>
                        <input type="date" class="form-control" id="endDate" name="end_date" 
                               min="{{ historical_data_range.start }}" max="{{ historical_data_range.end }}">
                    </div>
                    
                    <div class="mb-4">
                        <small class="text-light">
                            <i class="fas fa-info-circle me-1"></i>
                            Leave dates empty to use all available historical data
                        </small>
                    </div>
                    
                    <button type="submit" class="btn btn-light btn-lg w-100">
                        <i class="fas fa-play me-2"></i>
                        Generate Forecast
                    </button>
                </form>
            </div>
            
            <!-- Quick Presets -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        Quick Presets
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="setPreset('LSTM - Ensemble', 30)">
                            Best LSTM - 30 Days
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="setPreset('ARIMA - arima_1_1_1', 30)">
                            ARIMA(1,1,1) - 30 Days
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="setPreset('LSTM - Ensemble', 90)">
                            LSTM Quarterly Forecast
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Results Area -->
        <div class="col-lg-8">
            <div id="resultsArea" style="display: none;">
                <!-- Forecast Chart -->
                <div class="results-card">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            Forecast Visualization
                        </h5>
                    </div>
                    <div class="chart-container">
                        <img id="forecastChart" src="" alt="Forecast Chart" class="img-fluid" style="display: none;">
                        <div id="chartPlaceholder" class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                                <p class="text-muted">Generate a forecast to view the chart</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Forecast Summary -->
                <div class="results-card">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calculator text-success me-2"></i>
                            Forecast Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="forecastSummary">
                            <!-- Summary will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                
                <!-- Model Information -->
                <div class="results-card">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            Model Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="modelInfo">
                            <!-- Model info will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                
                <!-- Detailed Forecast Data -->
                <div class="results-card">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table text-secondary me-2"></i>
                            Detailed Forecast Data
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="forecast-table">
                            <table class="table table-striped" id="forecastTable">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Predicted Revenue</th>
                                        <th>Day of Week</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Table data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-primary" onclick="downloadForecast()">
                                <i class="fas fa-download me-2"></i>
                                Download CSV
                            </button>
                            <button class="btn btn-outline-secondary" onclick="copyToClipboard()">
                                <i class="fas fa-copy me-2"></i>
                                Copy Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Initial State -->
            <div id="initialState" class="text-center py-5">
                <i class="fas fa-crystal-ball fa-5x text-muted mb-4"></i>
                <h3 class="text-muted mb-3">Ready to Generate Forecasts</h3>
                <p class="text-muted">Configure your forecast parameters and click "Generate Forecast" to begin</p>
                <div class="mt-4">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-brain fa-2x text-primary mb-2"></i>
                                    <h6>LSTM Models</h6>
                                    <small class="text-muted">Advanced neural networks</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-area fa-2x text-success mb-2"></i>
                                    <h6>ARIMA Models</h6>
                                    <small class="text-muted">Statistical time series</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar-alt fa-2x text-warning mb-2"></i>
                                    <h6>Custom Periods</h6>
                                    <small class="text-muted">7 days to 1 year</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5>Generating Forecast...</h5>
        <p class="text-muted mb-0">This may take a few moments</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/forecast.js') }}"></script>
{% endblock %}
