#!/usr/bin/env python3
"""
Test the improved API
"""

import requests
import json

def test_forecast_api():
    """Test the forecast API"""
    print("Testing forecast API...")
    
    try:
        response = requests.post('http://localhost:5005/api/forecast', 
                               json={'steps': 7, 'confidence_intervals': True})
        
        if response.status_code == 200:
            data = response.json()
            print('✅ Forecast API working!')
            print(f'Model: ARIMA{data["model_metadata"]["order"]}')
            print(f'MAPE: {data["model_metadata"]["mape"]:.2f}%')
            print(f'7-day forecast summary:')
            print(f'  Mean: ${data["summary"]["mean"]:,.2f}')
            print(f'  Total: ${data["summary"]["total"]:,.2f}')
            print(f'  Trend: {data["summary"]["trend"]}')
            print(f'First 3 forecasts:')
            for i, forecast in enumerate(data['forecasts'][:3]):
                print(f'  {forecast["date"]}: ${forecast["value"]:,.2f}')
        else:
            print(f'❌ API Error: {response.status_code}')
            print(response.text)
            
    except Exception as e:
        print(f'❌ Error: {str(e)}')

def test_model_summary():
    """Test model summary API"""
    print("\nTesting model summary API...")
    
    try:
        response = requests.get('http://localhost:5005/api/model-summary')
        
        if response.status_code == 200:
            data = response.json()
            print('✅ Model summary API working!')
            print(f'Model: {data["model_info"]["Model"]}')
            print(f'MAPE: {data["model_info"]["MAPE"]:.2f}%')
            print(f'Quality: {data["diagnostics"]["quality"]}')
            print(f'File: {data["model_info"]["File"]}')
        else:
            print(f'❌ API Error: {response.status_code}')
            
    except Exception as e:
        print(f'❌ Error: {str(e)}')

if __name__ == "__main__":
    test_forecast_api()
    test_model_summary()
