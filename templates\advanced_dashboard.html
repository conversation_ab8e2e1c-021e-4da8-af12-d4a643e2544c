{% extends "base.html" %}

{% block title %}Advanced Dashboard - Time Series Forecasting{% endblock %}

{% block extra_head %}
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .metric-label {
        font-size: 0.9rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .model-performance-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .quick-action-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: transform 0.3s ease;
        text-decoration: none;
        display: block;
    }
    
    .quick-action-card:hover {
        transform: translateY(-5px);
        color: white;
        text-decoration: none;
    }
    
    .chart-preview {
        height: 300px;
        background: #f8f9fa;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1rem 0;
        border: 2px dashed #dee2e6;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }
    
    .status-excellent {
        background: rgba(25, 135, 84, 0.1);
        color: #0a3622;
        border: 1px solid rgba(25, 135, 84, 0.2);
    }
    
    .status-good {
        background: rgba(255, 193, 7, 0.1);
        color: #664d03;
        border: 1px solid rgba(255, 193, 7, 0.2);
    }
    
    .status-poor {
        background: rgba(220, 53, 69, 0.1);
        color: #58151c;
        border: 1px solid rgba(220, 53, 69, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-5 mb-0">
                        <i class="fas fa-chart-line text-primary me-3"></i>
                        Advanced Forecasting Dashboard
                    </h1>
                    <p class="text-muted mt-2">Multi-model time series forecasting with ARIMA and LSTM</p>
                </div>
                <div>
                    <a href="{{ url_for('forecast_page') }}" class="btn btn-primary btn-lg me-2">
                        <i class="fas fa-crystal-ball me-2"></i>Create Forecast
                    </a>
                    <a href="{{ url_for('model_comparison') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-chart-bar me-2"></i>Compare Models
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">{{ stats.total_models }}</div>
                <div class="metric-label">Total Models</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">{{ stats.arima_models }}</div>
                <div class="metric-label">ARIMA Models</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">{{ stats.lstm_models }}</div>
                <div class="metric-label">LSTM Models</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">{{ "{:,}".format(stats.data_points) }}</div>
                <div class="metric-label">Data Points</div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Left Column - Model Performance -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy text-warning me-2"></i>
                        Best Performing Models
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if stats.best_models.lstm %}
                        <div class="col-md-6">
                            <div class="model-performance-card">
                                <h6 class="mb-3">
                                    <i class="fas fa-brain me-2"></i>
                                    Best LSTM Model
                                </h6>
                                <h4 class="mb-2">{{ stats.best_models.lstm.name }}</h4>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <small>MAE</small>
                                        <div class="fw-bold">
                                            {% if stats.best_models.lstm.mae != 'N/A' %}
                                                {{ "%.3f"|format(stats.best_models.lstm.mae) }}%
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div>
                                        <small>MAPE</small>
                                        <div class="fw-bold">
                                            {% if stats.best_models.lstm.mape != 'N/A' %}
                                                {{ "%.3f"|format(stats.best_models.lstm.mape) }}%
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    {% if stats.best_models.lstm.mae != 'N/A' and stats.best_models.lstm.mape != 'N/A' %}
                                        {% if stats.best_models.lstm.mae < 5 and stats.best_models.lstm.mape < 5 %}
                                            <span class="status-badge status-excellent">
                                                <i class="fas fa-check-circle me-1"></i>Excellent
                                            </span>
                                        {% elif stats.best_models.lstm.mae < 10 and stats.best_models.lstm.mape < 10 %}
                                            <span class="status-badge status-good">
                                                <i class="fas fa-exclamation-circle me-1"></i>Good
                                            </span>
                                        {% else %}
                                            <span class="status-badge status-poor">
                                                <i class="fas fa-times-circle me-1"></i>Needs Improvement
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="status-badge status-poor">
                                            <i class="fas fa-exclamation-triangle me-1"></i>No Data
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if stats.best_models.arima %}
                        <div class="col-md-6">
                            <div class="model-performance-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                                <h6 class="mb-3">
                                    <i class="fas fa-chart-area me-2"></i>
                                    Best ARIMA Model
                                </h6>
                                <h4 class="mb-2">{{ stats.best_models.arima.name }}</h4>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <small>MAE</small>
                                        <div class="fw-bold">
                                            {% if stats.best_models.arima.mae != 'N/A' %}
                                                {{ "%.2f"|format(stats.best_models.arima.mae) }}%
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div>
                                        <small>MAPE</small>
                                        <div class="fw-bold">
                                            {% if stats.best_models.arima.mape != 'N/A' %}
                                                {{ "%.2f"|format(stats.best_models.arima.mape) }}%
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    {% if stats.best_models.arima.mae != 'N/A' and stats.best_models.arima.mape != 'N/A' %}
                                        {% if stats.best_models.arima.mae < 5 and stats.best_models.arima.mape < 5 %}
                                            <span class="status-badge status-excellent">
                                                <i class="fas fa-check-circle me-1"></i>Excellent
                                            </span>
                                        {% elif stats.best_models.arima.mae < 10 and stats.best_models.arima.mape < 10 %}
                                            <span class="status-badge status-good">
                                                <i class="fas fa-exclamation-circle me-1"></i>Good
                                            </span>
                                        {% else %}
                                            <span class="status-badge status-poor">
                                                <i class="fas fa-times-circle me-1"></i>Needs Improvement
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="status-badge status-poor">
                                            <i class="fas fa-exclamation-triangle me-1"></i>No Data
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Historical Data Overview -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database text-info me-2"></i>
                        Historical Data Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-preview">
                                <div class="text-center">
                                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Historical Revenue Trend</p>
                                    <button class="btn btn-primary btn-sm" onclick="loadHistoricalChart()">
                                        <i class="fas fa-play me-1"></i>Load Chart
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">Data Summary</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Date Range</small>
                                    <div class="fw-bold">{{ stats.date_range.start }}</div>
                                    <div class="fw-bold">to {{ stats.date_range.end }}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Total Points</small>
                                    <div class="fw-bold">{{ "{:,}".format(stats.data_points) }}</div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <small class="text-muted">Available Models</small>
                                <div class="mt-2">
                                    {% for model in stats.available_models[:5] %}
                                    <span class="badge bg-secondary me-1 mb-1">{{ model }}</span>
                                    {% endfor %}
                                    {% if stats.available_models|length > 5 %}
                                    <span class="badge bg-light text-dark">+{{ stats.available_models|length - 5 }} more</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Quick Actions -->
        <div class="col-lg-4">
            <div class="row">
                <div class="col-12">
                    <a href="{{ url_for('forecast_page') }}" class="quick-action-card mb-3">
                        <i class="fas fa-crystal-ball fa-3x mb-3"></i>
                        <h5 class="mb-2">Generate Forecast</h5>
                        <p class="mb-0">Create predictions with custom time periods</p>
                    </a>
                </div>
                
                <div class="col-12">
                    <a href="{{ url_for('model_comparison') }}" class="quick-action-card mb-3" 
                       style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                        <h5 class="mb-2">Compare Models</h5>
                        <p class="mb-0">Analyze performance across all models</p>
                    </a>
                </div>
                
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle text-info me-2"></i>
                                System Status
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Models Loaded</span>
                                <span class="badge bg-success">{{ stats.total_models }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Data Quality</span>
                                <span class="badge bg-success">Excellent</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span>API Status</span>
                                <span class="badge bg-success">Active</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function loadHistoricalChart() {
    // Placeholder for loading historical chart
    alert('Historical chart loading functionality will be implemented with Chart.js');
}

// Auto-refresh dashboard data every 30 seconds
setInterval(function() {
    // Refresh key metrics if needed
    console.log('Dashboard auto-refresh');
}, 30000);
</script>
{% endblock %}
