{"ensemble_metrics": {"train_metrics": {"mae": 285.82530018665733, "rmse": 1120.910941534945, "mae_percent": 0.5659668125250867, "mape": 0.5297442816984737, "rmse_percent": 2.219531973869125, "r2": 0.9935884925818675, "mean_actual": 50502.13084251968, "std_actual": 13998.807254961921, "mean_predicted": 50504.016943334565, "std_predicted": 13448.03832302174}, "val_metrics": {"mae": 0.24880887813924346, "rmse": 0.24880887813924346, "mae_percent": 0.0005250401851148895, "mape": 0.0005250401851148894, "rmse_percent": 0.0005250401851148895, "r2": -1.169368592644805e+21, "mean_actual": 47388.53999999999, "std_actual": 7.275957614183426e-12, "mean_predicted": 47388.78880887815, "std_predicted": 7.275957614183426e-12}, "test_metrics": {"mae": 23.830344069833625, "rmse": 550.5271960447733, "mae_percent": 0.0502301495411543, "mape": 0.03127220599481717, "rmse_percent": 1.1604139370697049, "r2": 0.8073129621062487, "mean_actual": 47442.311614678896, "std_actual": 1254.1587940032716, "mean_predicted": 47418.97797530524, "std_predicted": 704.1263087276287}}, "individual_metrics": {"neural_network": {"train": {"mae": 816.8453031620865, "rmse": 1545.2926825347035, "mae_percent": 1.6174472037808614, "mape": 1.6155653636019274, "rmse_percent": 3.0598563996306916, "r2": 0.9878146101472867, "mean_actual": 50502.13084251968, "std_actual": 13998.807254961921, "mean_predicted": 50934.045721008166, "std_predicted": 13576.727280852916}, "val": {"mae": 564.1376394672492, "rmse": 564.1376394672492, "mae_percent": 1.1904516143929507, "mape": 1.1904516143929507, "rmse_percent": 1.1904516143929507, "r2": -6.011596640632363e+27, "mean_actual": 47388.53999999999, "std_actual": 7.275957614183426e-12, "mean_predicted": 47952.67763946724, "std_predicted": 3.605736113751246e-11}, "test": {"mae": 600.8742068010462, "rmse": 1046.5270272815649, "mae_percent": 1.2665365289981625, "mape": 1.2375171075644134, "rmse_percent": 2.205893835404437, "r2": 0.3037008867197126, "mean_actual": 47442.311614678896, "std_actual": 1254.1587940032716, "mean_predicted": 47967.64245694274, "std_predicted": 349.03652325906666}}, "gradient_boosting": {"train": {"mae": 4.9163445622617745, "rmse": 20.36507961616561, "mae_percent": 0.009734925002654573, "mape": 0.008721450963251188, "rmse_percent": 0.04032518881167578, "r2": 0.9999978836370171, "mean_actual": 50502.13084251968, "std_actual": 13998.807254961921, "mean_predicted": 50502.19447706815, "std_predicted": 13997.484613046066}, "val": {"mae": 0.043543521911487915, "rmse": 0.043543521911487915, "mae_percent": 9.188618579827089e-05, "mape": 9.188618579827087e-05, "rmse_percent": 9.188618579827089e-05, "r2": -3.5815150881246474e+19, "mean_actual": 47388.53999999999, "std_actual": 7.275957614183426e-12, "mean_predicted": 47388.58354352192, "std_predicted": 7.275957614183426e-12}, "test": {"mae": 4.015727255565788, "rmse": 92.73343836695017, "mae_percent": 0.008464442643902077, "mape": 0.005271079733576515, "rmse_percent": 0.19546568287000998, "r2": 0.9945327658998625, "mean_actual": 47442.311614678896, "std_actual": 1254.1587940032716, "mean_predicted": 47438.38281467442, "std_predicted": 1161.508617954428}}, "random_forest": {"train": {"mae": 789.05132766201, "rmse": 3151.868546442606, "mae_percent": 1.5624119507402596, "mape": 1.474536982432729, "rmse_percent": 6.241060513408926, "r2": 0.9493062843218637, "mean_actual": 50502.13084251968, "std_actual": 13998.807254961921, "mean_predicted": 50507.298111232376, "std_predicted": 12625.824378656273}, "val": {"mae": 0.12305632595234783, "rmse": 0.12305632595234783, "mae_percent": 0.0002596752842614435, "mape": 0.00025967528426144344, "rmse_percent": 0.0002596752842614435, "r2": -2.8604052592715425e+20, "mean_actual": 47388.53999999999, "std_actual": 7.275957614183426e-12, "mean_predicted": 47388.41694367404, "std_predicted": 7.275957614183426e-12}, "test": {"mae": 46.95211873874043, "rmse": 1093.240747726105, "mae_percent": 0.09896676013614228, "mape": 0.061319054166089994, "rmse_percent": 2.3043580941107655, "r2": 0.24015227832297525, "mean_actual": 47442.311614678896, "std_actual": 1254.1587940032716, "mean_predicted": 47395.35949594015, "std_predicted": 161.92675316456956}}, "ridge_regression": {"train": {"mae": 99.63991591429208, "rmse": 331.5836017863956, "mae_percent": 0.19729843919853263, "mape": 0.17903459681925057, "rmse_percent": 0.656573487602671, "r2": 0.999438946828621, "mean_actual": 50502.13084251968, "std_actual": 13998.807254961921, "mean_predicted": 50502.13084251897, "std_predicted": 13909.80600691698}, "val": {"mae": 0.26584422628366156, "rmse": 0.26584422628366156, "mae_percent": 0.0005609884294465742, "mape": 0.0005609884294465742, "rmse_percent": 0.0005609884294465742, "r2": -1.334978109221356e+21, "mean_actual": 47388.53999999999, "std_actual": 7.275957614183426e-12, "mean_predicted": 47388.80584422628, "std_predicted": 7.275957614183426e-12}, "test": {"mae": 20.19586552686011, "rmse": 465.2824953325855, "mae_percent": 0.04256931173777671, "mape": 0.026546986627273048, "rmse_percent": 0.9807331883647605, "r2": 0.8623651631011479, "mean_actual": 47442.311614678896, "std_actual": 1254.1587940032716, "mean_predicted": 47422.64646202946, "std_predicted": 789.2920580087589}}}, "target_mae": 5.0, "target_mape": 5.0, "model_params": {"sequence_length": 30, "ensemble_weights": {"neural_network": 0.00033100279252251983, "gradient_boosting": 0.3332517895461818, "random_forest": 0.3332378385544367, "ridge_regression": 0.33317936910685897}, "models_used": ["neural_network", "gradient_boosting", "random_forest", "ridge_regression"]}}