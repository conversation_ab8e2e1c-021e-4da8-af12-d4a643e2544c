#!/usr/bin/env python3
"""
LSTM vs ARIMA Comprehensive Comparison Report
Detailed analysis comparing Advanced LSTM-style ensemble model with ARIMA models
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def load_results():
    """Load all model results for comparison"""
    results = {}
    
    # Load ARIMA results
    try:
        with open('models/model_evaluation_comprehensive.json', 'r') as f:
            arima_data = json.load(f)
        results['arima'] = arima_data
        print("✅ ARIMA results loaded")
    except FileNotFoundError:
        print("❌ ARIMA results not found")
        results['arima'] = None
    
    # Load Advanced LSTM results
    try:
        with open('models/advanced_lstm_evaluation_results.json', 'r') as f:
            lstm_data = json.load(f)
        results['lstm'] = lstm_data
        print("✅ Advanced LSTM results loaded")
    except FileNotFoundError:
        print("❌ Advanced LSTM results not found")
        results['lstm'] = None
    
    return results

def create_comparison_report(results):
    """Create comprehensive comparison report"""
    print("\n" + "="*80)
    print("COMPREHENSIVE MODEL COMPARISON REPORT")
    print("="*80)
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # Performance targets
    target_mae = 5.0
    target_mape = 5.0
    
    print(f"\n🎯 PERFORMANCE TARGETS:")
    print(f"   MAE: <{target_mae}%")
    print(f"   MAPE: <{target_mape}%")
    
    # Model comparison table
    comparison_data = []
    
    if results['arima']:
        # ARIMA(1,1,1) results
        if 'arima_1_1_1' in results['arima']:
            arima_111 = results['arima']['arima_1_1_1']['test_metrics']
            comparison_data.append({
                'Model': 'ARIMA(1,1,1)',
                'MAE (%)': arima_111['mae_percent'],
                'MAPE (%)': arima_111['mape'],
                'RMSE (%)': arima_111['rmse_percent'],
                'R²': arima_111['r2'],
                'MAE Target': '✅' if arima_111['mae_percent'] < target_mae else '❌',
                'MAPE Target': '✅' if arima_111['mape'] < target_mape else '❌'
            })
        
        # ARIMA(1,1,2) results
        if 'arima_1_1_2' in results['arima']:
            arima_112 = results['arima']['arima_1_1_2']['test_metrics']
            comparison_data.append({
                'Model': 'ARIMA(1,1,2)',
                'MAE (%)': arima_112['mae_percent'],
                'MAPE (%)': arima_112['mape'],
                'RMSE (%)': arima_112['rmse_percent'],
                'R²': arima_112['r2'],
                'MAE Target': '✅' if arima_112['mae_percent'] < target_mae else '❌',
                'MAPE Target': '✅' if arima_112['mape'] < target_mape else '❌'
            })
    
    if results['lstm']:
        # Advanced LSTM Ensemble results
        lstm_test = results['lstm']['ensemble_metrics']['test_metrics']
        comparison_data.append({
            'Model': 'Advanced LSTM Ensemble',
            'MAE (%)': lstm_test['mae_percent'],
            'MAPE (%)': lstm_test['mape'],
            'RMSE (%)': lstm_test['rmse_percent'],
            'R²': lstm_test['r2'],
            'MAE Target': '✅' if lstm_test['mae_percent'] < target_mae else '❌',
            'MAPE Target': '✅' if lstm_test['mape'] < target_mape else '❌'
        })
        
        # Individual LSTM models
        individual_models = results['lstm']['individual_metrics']
        for model_name, metrics in individual_models.items():
            test_metrics = metrics['test']
            comparison_data.append({
                'Model': f"LSTM - {model_name.replace('_', ' ').title()}",
                'MAE (%)': test_metrics['mae_percent'],
                'MAPE (%)': test_metrics['mape'],
                'RMSE (%)': test_metrics['rmse_percent'],
                'R²': test_metrics['r2'],
                'MAE Target': '✅' if test_metrics['mae_percent'] < target_mae else '❌',
                'MAPE Target': '✅' if test_metrics['mape'] < target_mape else '❌'
            })
    
    # Create DataFrame for better formatting
    df_comparison = pd.DataFrame(comparison_data)
    
    print(f"\n📊 MODEL PERFORMANCE COMPARISON (Test Set):")
    print("="*80)
    print(df_comparison.to_string(index=False, float_format='%.4f'))
    
    # Find best performing models
    if len(comparison_data) > 0:
        best_mae = min(comparison_data, key=lambda x: x['MAE (%)'])
        best_mape = min(comparison_data, key=lambda x: x['MAPE (%)'])
        best_r2 = max(comparison_data, key=lambda x: x['R²'])
        
        print(f"\n🏆 BEST PERFORMING MODELS:")
        print(f"   Best MAE: {best_mae['Model']} ({best_mae['MAE (%)']:.4f}%)")
        print(f"   Best MAPE: {best_mape['Model']} ({best_mape['MAPE (%)']:.4f}%)")
        print(f"   Best R²: {best_r2['Model']} ({best_r2['R²']:.4f})")
        
        # Target achievement summary
        targets_met = [model for model in comparison_data 
                      if model['MAE Target'] == '✅' and model['MAPE Target'] == '✅']
        
        print(f"\n🎯 TARGET ACHIEVEMENT SUMMARY:")
        print(f"   Models meeting both targets: {len(targets_met)}/{len(comparison_data)}")
        
        if targets_met:
            print(f"   ✅ Models achieving targets:")
            for model in targets_met:
                print(f"      - {model['Model']}: MAE {model['MAE (%)']:.4f}%, MAPE {model['MAPE (%)']:.4f}%")
        
        # Performance improvements
        if results['arima'] and results['lstm']:
            print(f"\n📈 LSTM vs ARIMA IMPROVEMENTS:")
            
            # Compare with ARIMA(1,1,1) if available
            arima_111_data = next((model for model in comparison_data if model['Model'] == 'ARIMA(1,1,1)'), None)
            lstm_ensemble_data = next((model for model in comparison_data if model['Model'] == 'Advanced LSTM Ensemble'), None)
            
            if arima_111_data and lstm_ensemble_data:
                mae_improvement = ((arima_111_data['MAE (%)'] - lstm_ensemble_data['MAE (%)']) / arima_111_data['MAE (%)']) * 100
                mape_improvement = ((arima_111_data['MAPE (%)'] - lstm_ensemble_data['MAPE (%)']) / arima_111_data['MAPE (%)']) * 100
                r2_improvement = lstm_ensemble_data['R²'] - arima_111_data['R²']
                
                print(f"   Advanced LSTM vs ARIMA(1,1,1):")
                print(f"      MAE Improvement: {mae_improvement:.1f}%")
                print(f"      MAPE Improvement: {mape_improvement:.1f}%")
                print(f"      R² Improvement: {r2_improvement:.4f}")
                
                if mae_improvement > 0 and mape_improvement > 0:
                    print(f"      🏆 LSTM significantly outperforms ARIMA!")
                elif mae_improvement > 0 or mape_improvement > 0:
                    print(f"      ⚡ LSTM outperforms ARIMA on one metric")
                else:
                    print(f"      📊 ARIMA performs better")
    
    return df_comparison

def create_visualization(results):
    """Create comprehensive visualization comparing all models"""
    if not results['lstm']:
        print("❌ Cannot create visualization without LSTM results")
        return
    
    print(f"\n📊 Creating comprehensive comparison visualization...")
    
    # Prepare data for visualization
    models = []
    mae_values = []
    mape_values = []
    r2_values = []
    
    # Add ARIMA results if available
    if results['arima']:
        if 'arima_1_1_1' in results['arima']:
            models.append('ARIMA(1,1,1)')
            mae_values.append(results['arima']['arima_1_1_1']['test_metrics']['mae_percent'])
            mape_values.append(results['arima']['arima_1_1_1']['test_metrics']['mape'])
            r2_values.append(results['arima']['arima_1_1_1']['test_metrics']['r2'])
        
        if 'arima_1_1_2' in results['arima']:
            models.append('ARIMA(1,1,2)')
            mae_values.append(results['arima']['arima_1_1_2']['test_metrics']['mae_percent'])
            mape_values.append(results['arima']['arima_1_1_2']['test_metrics']['mape'])
            r2_values.append(results['arima']['arima_1_1_2']['test_metrics']['r2'])
    
    # Add LSTM results
    models.append('LSTM Ensemble')
    lstm_test = results['lstm']['ensemble_metrics']['test_metrics']
    mae_values.append(lstm_test['mae_percent'])
    mape_values.append(lstm_test['mape'])
    r2_values.append(lstm_test['r2'])
    
    # Add individual LSTM models
    for model_name, metrics in results['lstm']['individual_metrics'].items():
        models.append(f"LSTM-{model_name.replace('_', ' ').title()}")
        test_metrics = metrics['test']
        mae_values.append(test_metrics['mae_percent'])
        mape_values.append(test_metrics['mape'])
        r2_values.append(test_metrics['r2'])
    
    # Create visualization
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('Comprehensive Model Comparison: LSTM vs ARIMA', fontsize=16, fontweight='bold')
    
    # Plot 1: MAE Comparison
    colors = ['red' if 'ARIMA' in model else 'blue' if 'Ensemble' in model else 'green' for model in models]
    bars1 = axes[0, 0].bar(range(len(models)), mae_values, color=colors, alpha=0.7)
    axes[0, 0].axhline(y=5.0, color='red', linestyle='--', linewidth=2, label='Target (5%)')
    axes[0, 0].set_title('Mean Absolute Error (%) Comparison', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('Models')
    axes[0, 0].set_ylabel('MAE (%)')
    axes[0, 0].set_xticks(range(len(models)))
    axes[0, 0].set_xticklabels(models, rotation=45, ha='right')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars1, mae_values)):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.1,
                       f'{value:.3f}%', ha='center', va='bottom', fontweight='bold')
    
    # Plot 2: MAPE Comparison
    bars2 = axes[0, 1].bar(range(len(models)), mape_values, color=colors, alpha=0.7)
    axes[0, 1].axhline(y=5.0, color='red', linestyle='--', linewidth=2, label='Target (5%)')
    axes[0, 1].set_title('Mean Absolute Percentage Error (%) Comparison', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('Models')
    axes[0, 1].set_ylabel('MAPE (%)')
    axes[0, 1].set_xticks(range(len(models)))
    axes[0, 1].set_xticklabels(models, rotation=45, ha='right')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars2, mape_values)):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.1,
                       f'{value:.3f}%', ha='center', va='bottom', fontweight='bold')
    
    # Plot 3: R² Comparison
    bars3 = axes[1, 0].bar(range(len(models)), r2_values, color=colors, alpha=0.7)
    axes[1, 0].set_title('R² Score Comparison', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('Models')
    axes[1, 0].set_ylabel('R² Score')
    axes[1, 0].set_xticks(range(len(models)))
    axes[1, 0].set_xticklabels(models, rotation=45, ha='right')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars3, r2_values)):
        axes[1, 0].text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                       f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # Plot 4: Target Achievement Summary
    target_achievement = []
    for i, model in enumerate(models):
        mae_achieved = mae_values[i] < 5.0
        mape_achieved = mape_values[i] < 5.0
        if mae_achieved and mape_achieved:
            target_achievement.append(2)  # Both targets
        elif mae_achieved or mape_achieved:
            target_achievement.append(1)  # One target
        else:
            target_achievement.append(0)  # No targets
    
    colors_achievement = ['red' if x == 0 else 'orange' if x == 1 else 'green' for x in target_achievement]
    bars4 = axes[1, 1].bar(range(len(models)), target_achievement, color=colors_achievement, alpha=0.7)
    axes[1, 1].set_title('Target Achievement Summary', fontsize=14, fontweight='bold')
    axes[1, 1].set_xlabel('Models')
    axes[1, 1].set_ylabel('Targets Achieved')
    axes[1, 1].set_xticks(range(len(models)))
    axes[1, 1].set_xticklabels(models, rotation=45, ha='right')
    axes[1, 1].set_yticks([0, 1, 2])
    axes[1, 1].set_yticklabels(['None', 'One', 'Both'])
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('comprehensive_model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Comprehensive comparison visualization saved as 'comprehensive_model_comparison.png'")

def main():
    """Main function to generate comprehensive comparison report"""
    print("🚀 GENERATING COMPREHENSIVE LSTM vs ARIMA COMPARISON REPORT")
    print("="*80)
    
    # Load results
    results = load_results()
    
    # Create comparison report
    df_comparison = create_comparison_report(results)
    
    # Create visualization
    create_visualization(results)
    
    # Save comparison data
    if len(df_comparison) > 0:
        df_comparison.to_csv('model_comparison_summary.csv', index=False)
        print(f"\n💾 Comparison data saved as 'model_comparison_summary.csv'")
    
    print(f"\n🏁 COMPREHENSIVE COMPARISON REPORT COMPLETED")
    print("="*80)

if __name__ == "__main__":
    main()
