{% extends "base.html" %}

{% block title %}Model Comparison - Time Series Dashboard{% endblock %}

{% block extra_head %}
<style>
    .comparison-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .model-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: transform 0.3s ease;
    }
    
    .model-card:hover {
        transform: translateY(-5px);
    }
    
    .model-card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #eee;
    }
    
    .model-card-body {
        padding: 1.5rem;
    }
    
    .lstm-card .model-card-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    
    .arima-card .model-card-header {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }
    
    .performance-metric {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
        text-align: center;
    }
    
    .metric-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    .metric-label {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .performance-excellent {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .performance-good {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .performance-poor {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .comparison-chart {
        height: 400px;
        background: #f8f9fa;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1rem 0;
    }
    
    .target-indicator {
        position: relative;
        padding: 0.5rem;
        border-radius: 5px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .target-met {
        background: rgba(25, 135, 84, 0.1);
        color: #0a3622;
        border: 1px solid rgba(25, 135, 84, 0.2);
    }
    
    .target-missed {
        background: rgba(220, 53, 69, 0.1);
        color: #58151c;
        border: 1px solid rgba(220, 53, 69, 0.2);
    }
    
    .model-type-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .badge-lstm {
        background: rgba(79, 172, 254, 0.1);
        color: #0066cc;
        border: 1px solid rgba(79, 172, 254, 0.3);
    }
    
    .badge-arima {
        background: rgba(247, 112, 154, 0.1);
        color: #cc0066;
        border: 1px solid rgba(247, 112, 154, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="comparison-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="display-5 mb-0">
                    <i class="fas fa-chart-bar me-3"></i>
                    Model Performance Comparison
                </h1>
                <p class="mt-2 mb-0 opacity-75">Compare ARIMA and LSTM models across key performance metrics</p>
            </div>
            <div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-light btn-lg me-2">
                    <i class="fas fa-arrow-left me-2"></i>Dashboard
                </a>
                <a href="{{ url_for('forecast_page') }}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-crystal-ball me-2"></i>Forecast
                </a>
            </div>
        </div>
    </div>

    <!-- Performance Targets -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bullseye text-warning me-2"></i>
                        Performance Targets
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-target fa-2x text-success me-3"></i>
                                <div>
                                    <h6 class="mb-1">Mean Absolute Error (MAE)</h6>
                                    <p class="mb-0 text-muted">Target: <strong>&lt; 5%</strong></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-crosshairs fa-2x text-info me-3"></i>
                                <div>
                                    <h6 class="mb-1">Mean Absolute Percentage Error (MAPE)</h6>
                                    <p class="mb-0 text-muted">Target: <strong>&lt; 5%</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Comparison Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        Performance Comparison Chart
                    </h5>
                </div>
                <div class="card-body">
                    <div class="comparison-chart" id="comparisonChart">
                        <div class="text-center">
                            <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                            <p class="text-muted">Loading performance comparison chart...</p>
                            <button class="btn btn-primary" onclick="loadComparisonChart()">
                                <i class="fas fa-play me-2"></i>Load Chart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Cards -->
    <div class="row" id="modelCards">
        <!-- Model cards will be populated by JavaScript -->
    </div>

    <!-- Detailed Comparison Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table text-secondary me-2"></i>
                        Detailed Performance Metrics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="performanceTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Model</th>
                                    <th>Type</th>
                                    <th>MAE (%)</th>
                                    <th>MAPE (%)</th>
                                    <th>RMSE (%)</th>
                                    <th>R²</th>
                                    <th>Target Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Table data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadModelComparison();
});

async function loadModelComparison() {
    console.log('📊 Loading model comparison data...');
    
    try {
        const response = await fetch('/api/model-performance');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('✅ Model performance data loaded:', data);
        
        displayModelCards(data.performance_data, data.targets);
        displayPerformanceTable(data.performance_data, data.targets);
        
    } catch (error) {
        console.error('❌ Failed to load model comparison:', error);
        showError('Failed to load model comparison data: ' + error.message);
    }
}

function displayModelCards(performanceData, targets) {
    const modelCardsContainer = document.getElementById('modelCards');
    if (!modelCardsContainer) return;
    
    modelCardsContainer.innerHTML = '';
    
    // Group models by type
    const lstmModels = performanceData.filter(model => model.type === 'LSTM');
    const arimaModels = performanceData.filter(model => model.type === 'ARIMA');
    
    // Display LSTM models
    if (lstmModels.length > 0) {
        const lstmSection = createModelSection('LSTM Models', lstmModels, targets, 'lstm');
        modelCardsContainer.appendChild(lstmSection);
    }
    
    // Display ARIMA models
    if (arimaModels.length > 0) {
        const arimaSection = createModelSection('ARIMA Models', arimaModels, targets, 'arima');
        modelCardsContainer.appendChild(arimaSection);
    }
}

function createModelSection(title, models, targets, type) {
    const section = document.createElement('div');
    section.className = 'col-12 mb-4';
    
    const sectionHeader = document.createElement('h4');
    sectionHeader.className = 'mb-3';
    sectionHeader.innerHTML = `<i class="fas fa-${type === 'lstm' ? 'brain' : 'chart-area'} me-2"></i>${title}`;
    
    const modelsRow = document.createElement('div');
    modelsRow.className = 'row';
    
    models.forEach(model => {
        const modelCard = createModelCard(model, targets, type);
        modelsRow.appendChild(modelCard);
    });
    
    section.appendChild(sectionHeader);
    section.appendChild(modelsRow);
    
    return section;
}

function createModelCard(model, targets, type) {
    const col = document.createElement('div');
    col.className = 'col-lg-4 col-md-6';
    
    const maeStatus = model.mae_percent < targets.mae_target ? 'excellent' : 'poor';
    const mapeStatus = model.mape < targets.mape_target ? 'excellent' : 'poor';
    const overallStatus = (maeStatus === 'excellent' && mapeStatus === 'excellent') ? 'excellent' : 'poor';
    
    col.innerHTML = `
        <div class="model-card ${type}-card">
            <div class="model-card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">${model.model}</h6>
                    <span class="model-type-badge badge-${type}">${type.toUpperCase()}</span>
                </div>
            </div>
            <div class="model-card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="performance-metric performance-${maeStatus}">
                            <div class="metric-value">${model.mae_percent.toFixed(3)}%</div>
                            <div class="metric-label">MAE</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="performance-metric performance-${mapeStatus}">
                            <div class="metric-value">${model.mape.toFixed(3)}%</div>
                            <div class="metric-label">MAPE</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="performance-metric">
                            <div class="metric-value">${model.rmse_percent.toFixed(3)}%</div>
                            <div class="metric-label">RMSE</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="performance-metric">
                            <div class="metric-value">${model.r2.toFixed(4)}</div>
                            <div class="metric-label">R²</div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <div class="target-indicator ${overallStatus === 'excellent' ? 'target-met' : 'target-missed'}">
                        <i class="fas fa-${overallStatus === 'excellent' ? 'check-circle' : 'times-circle'} me-2"></i>
                        ${overallStatus === 'excellent' ? 'Meets Performance Targets' : 'Below Performance Targets'}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return col;
}

function displayPerformanceTable(performanceData, targets) {
    const tableBody = document.querySelector('#performanceTable tbody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    // Sort by overall performance (MAE + MAPE)
    const sortedData = performanceData.sort((a, b) => {
        const scoreA = a.mae_percent + a.mape;
        const scoreB = b.mae_percent + b.mape;
        return scoreA - scoreB;
    });
    
    sortedData.forEach((model, index) => {
        const maeTargetMet = model.mae_percent < targets.mae_target;
        const mapeTargetMet = model.mape < targets.mape_target;
        const overallTargetMet = maeTargetMet && mapeTargetMet;
        
        const row = document.createElement('tr');
        if (index === 0) {
            row.classList.add('table-success'); // Highlight best model
        }
        
        row.innerHTML = `
            <td>
                <strong>${model.model}</strong>
                ${index === 0 ? '<span class="badge bg-warning text-dark ms-2">Best</span>' : ''}
            </td>
            <td><span class="badge badge-${model.type.toLowerCase()}">${model.type}</span></td>
            <td class="${maeTargetMet ? 'text-success fw-bold' : 'text-danger'}">${model.mae_percent.toFixed(3)}%</td>
            <td class="${mapeTargetMet ? 'text-success fw-bold' : 'text-danger'}">${model.mape.toFixed(3)}%</td>
            <td>${model.rmse_percent.toFixed(3)}%</td>
            <td>${model.r2.toFixed(4)}</td>
            <td>
                <span class="target-indicator ${overallTargetMet ? 'target-met' : 'target-missed'}">
                    <i class="fas fa-${overallTargetMet ? 'check' : 'times'} me-1"></i>
                    ${overallTargetMet ? 'Met' : 'Missed'}
                </span>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
}

function loadComparisonChart() {
    const chartContainer = document.getElementById('comparisonChart');
    if (chartContainer) {
        chartContainer.innerHTML = `
            <div class="text-center">
                <i class="fas fa-chart-bar fa-4x text-primary mb-3"></i>
                <p class="text-muted">Interactive comparison chart would be implemented with Chart.js</p>
                <p class="text-muted">Showing MAE, MAPE, RMSE, and R² across all models</p>
            </div>
        `;
    }
}

function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 10000; max-width: 400px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Error:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}
