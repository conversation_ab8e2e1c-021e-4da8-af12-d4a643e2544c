#!/usr/bin/env python3
"""
PyTorch LSTM Time Series Forecasting Model
Advanced LSTM implementation for daily revenue forecasting with target performance <5% MAE and MAPE
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import warnings
import pickle
import json
from datetime import datetime, timedelta
import os

warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)
torch.manual_seed(42)

# Check for GPU availability
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

class TimeSeriesDataset(Dataset):
    """Custom Dataset for time series data"""
    
    def __init__(self, sequences, targets):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

class LSTMModel(nn.Module):
    """Advanced LSTM Model for Time Series Forecasting"""
    
    def __init__(self, input_size, hidden_size=128, num_layers=3, dropout=0.3):
        super(LSTMModel, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout,
            batch_first=True
        )
        
        # Batch normalization
        self.batch_norm = nn.BatchNorm1d(hidden_size)
        
        # Dense layers
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, hidden_size // 4)
        self.fc3 = nn.Linear(hidden_size // 4, 1)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # Activation
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # LSTM forward pass
        lstm_out, _ = self.lstm(x)
        
        # Take the last output
        lstm_out = lstm_out[:, -1, :]
        
        # Batch normalization
        lstm_out = self.batch_norm(lstm_out)
        
        # Dense layers with dropout
        out = self.relu(self.fc1(lstm_out))
        out = self.dropout(out)
        out = self.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        
        return out

class PyTorchLSTMForecaster:
    """PyTorch-based LSTM Forecasting Model"""
    
    def __init__(self, sequence_length=30, target_mae=5.0, target_mape=5.0):
        self.sequence_length = sequence_length
        self.target_mae = target_mae
        self.target_mape = target_mape
        self.model = None
        self.scaler = None
        self.feature_scaler = None
        self.training_history = {'train_loss': [], 'val_loss': []}
        self.evaluation_results = {}
        self.device = device
        
    def load_and_prepare_data(self):
        """Load and prepare data for LSTM training"""
        print("="*60)
        print("LOADING AND PREPARING DATA FOR PYTORCH LSTM")
        print("="*60)
        
        # Load the cleaned daily revenue time series
        print("📊 Loading daily revenue time series...")
        daily_ts = pd.read_csv('daily_revenue_timeseries.csv')
        daily_ts['Date'] = pd.to_datetime(daily_ts['Date'])
        daily_ts = daily_ts.sort_values('Date').reset_index(drop=True)
        
        print(f"✅ Loaded {len(daily_ts)} daily observations")
        print(f"   Date range: {daily_ts['Date'].min()} to {daily_ts['Date'].max()}")
        print(f"   Revenue range: ${daily_ts['DailyRevenue'].min():,.2f} to ${daily_ts['DailyRevenue'].max():,.2f}")
        
        # Create additional features for LSTM
        print("\n🔧 Creating advanced features...")
        daily_ts = self.create_advanced_features(daily_ts)
        
        # Handle missing dates (create continuous time series)
        print("\n📅 Creating continuous time series...")
        daily_ts = self.create_continuous_timeseries(daily_ts)
        
        print(f"✅ Final dataset: {len(daily_ts)} observations")
        return daily_ts
    
    def create_advanced_features(self, df):
        """Create advanced features for LSTM model"""
        df = df.copy()
        
        # Time-based features
        df['Year'] = df['Date'].dt.year
        df['Month'] = df['Date'].dt.month
        df['Day'] = df['Date'].dt.day
        df['DayOfWeek'] = df['Date'].dt.dayofweek
        df['DayOfYear'] = df['Date'].dt.dayofyear
        df['WeekOfYear'] = df['Date'].dt.isocalendar().week
        df['Quarter'] = df['Date'].dt.quarter
        
        # Cyclical encoding for time features
        df['Month_sin'] = np.sin(2 * np.pi * df['Month'] / 12)
        df['Month_cos'] = np.cos(2 * np.pi * df['Month'] / 12)
        df['DayOfWeek_sin'] = np.sin(2 * np.pi * df['DayOfWeek'] / 7)
        df['DayOfWeek_cos'] = np.cos(2 * np.pi * df['DayOfWeek'] / 7)
        df['DayOfYear_sin'] = np.sin(2 * np.pi * df['DayOfYear'] / 365)
        df['DayOfYear_cos'] = np.cos(2 * np.pi * df['DayOfYear'] / 365)
        
        # Lag features
        for lag in [1, 2, 3, 7, 14]:
            df[f'Revenue_lag_{lag}'] = df['DailyRevenue'].shift(lag)
            df[f'Quantity_lag_{lag}'] = df['DailyQuantity'].shift(lag)
            df[f'Transactions_lag_{lag}'] = df['DailyTransactions'].shift(lag)
        
        # Rolling statistics
        for window in [7, 14, 30]:
            df[f'Revenue_rolling_mean_{window}'] = df['DailyRevenue'].rolling(window=window).mean()
            df[f'Revenue_rolling_std_{window}'] = df['DailyRevenue'].rolling(window=window).std()
        
        # Exponential moving averages
        for alpha in [0.1, 0.3, 0.5]:
            df[f'Revenue_ema_{alpha}'] = df['DailyRevenue'].ewm(alpha=alpha).mean()
        
        # Revenue ratios and differences
        df['Revenue_pct_change'] = df['DailyRevenue'].pct_change()
        df['Revenue_diff'] = df['DailyRevenue'].diff()
        df['Avg_transaction_value'] = df['DailyRevenue'] / df['DailyTransactions']
        df['Avg_items_per_transaction'] = df['DailyQuantity'] / df['DailyTransactions']
        
        print(f"   Created {len(df.columns) - 4} additional features")
        return df
    
    def create_continuous_timeseries(self, df):
        """Create continuous time series by filling missing dates"""
        # Create complete date range
        date_range = pd.date_range(start=df['Date'].min(), end=df['Date'].max(), freq='D')
        complete_df = pd.DataFrame({'Date': date_range})
        
        # Merge with existing data
        df_complete = complete_df.merge(df, on='Date', how='left')
        
        # Forward fill missing values for most features
        numeric_cols = df_complete.select_dtypes(include=[np.number]).columns
        df_complete[numeric_cols] = df_complete[numeric_cols].fillna(method='ffill')
        
        # For revenue-related columns, use interpolation
        revenue_cols = [col for col in df_complete.columns if 'Revenue' in col or 'Quantity' in col or 'Transaction' in col]
        for col in revenue_cols:
            if col in df_complete.columns:
                df_complete[col] = df_complete[col].interpolate(method='linear')
        
        # Fill any remaining NaN values
        df_complete = df_complete.fillna(method='bfill').fillna(method='ffill')
        
        return df_complete
    
    def prepare_sequences(self, data, target_col='DailyRevenue'):
        """Prepare sequences for LSTM training"""
        print(f"\n🔄 Preparing sequences (length={self.sequence_length})...")
        
        # Select features for training (exclude Date and target)
        feature_cols = [col for col in data.columns if col not in ['Date', target_col]]
        
        # Add target column to features for sequence creation
        all_cols = feature_cols + [target_col]
        
        # Scale the data
        self.scaler = MinMaxScaler()
        self.feature_scaler = MinMaxScaler()
        
        # Scale target variable separately
        target_data = data[target_col].values.reshape(-1, 1)
        scaled_target = self.scaler.fit_transform(target_data)
        
        # Scale all features including target for sequence creation
        all_data = data[all_cols].values
        scaled_all_data = self.feature_scaler.fit_transform(all_data)
        
        # Create sequences
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_all_data)):
            X.append(scaled_all_data[i-self.sequence_length:i])
            y.append(scaled_target[i, 0])
        
        X, y = np.array(X), np.array(y)
        
        print(f"✅ Created {len(X)} sequences")
        print(f"   Input shape: {X.shape}")
        print(f"   Output shape: {y.shape}")
        
        return X, y, data.iloc[self.sequence_length:]['Date'].values
    
    def split_data(self, X, y, dates, train_ratio=0.7, val_ratio=0.15):
        """Split data into train, validation, and test sets"""
        n_samples = len(X)
        train_size = int(n_samples * train_ratio)
        val_size = int(n_samples * val_ratio)
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        dates_train = dates[:train_size]
        
        X_val = X[train_size:train_size + val_size]
        y_val = y[train_size:train_size + val_size]
        dates_val = dates[train_size:train_size + val_size]
        
        X_test = X[train_size + val_size:]
        y_test = y[train_size + val_size:]
        dates_test = dates[train_size + val_size:]
        
        print(f"\n📊 Data split:")
        print(f"   Training: {len(X_train)} samples ({train_ratio*100:.1f}%)")
        print(f"   Validation: {len(X_val)} samples ({val_ratio*100:.1f}%)")
        print(f"   Testing: {len(X_test)} samples ({(1-train_ratio-val_ratio)*100:.1f}%)")
        
        return (X_train, y_train, dates_train), (X_val, y_val, dates_val), (X_test, y_test, dates_test)

    def train_model(self, train_data, val_data, epochs=200, batch_size=32, learning_rate=0.001):
        """Train the PyTorch LSTM model"""
        X_train, y_train, _ = train_data
        X_val, y_val, _ = val_data

        print(f"\n🚀 Training PyTorch LSTM model...")
        print(f"   Epochs: {epochs}")
        print(f"   Batch size: {batch_size}")
        print(f"   Learning rate: {learning_rate}")

        # Create datasets and data loaders
        train_dataset = TimeSeriesDataset(X_train, y_train)
        val_dataset = TimeSeriesDataset(X_val, y_val)

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

        # Initialize model
        input_size = X_train.shape[2]
        self.model = LSTMModel(input_size=input_size, hidden_size=128, num_layers=3, dropout=0.3)
        self.model.to(self.device)

        # Loss function and optimizer
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10, verbose=True)

        print(f"✅ Model initialized with {sum(p.numel() for p in self.model.parameters()):,} parameters")

        # Training loop
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 20

        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0

            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)

                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                optimizer.step()
                train_loss += loss.item()

            # Validation phase
            self.model.eval()
            val_loss = 0.0

            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                    outputs = self.model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    val_loss += loss.item()

            # Calculate average losses
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(val_loader)

            # Store history
            self.training_history['train_loss'].append(avg_train_loss)
            self.training_history['val_loss'].append(avg_val_loss)

            # Learning rate scheduling
            scheduler.step(avg_val_loss)

            # Early stopping
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                # Save best model
                os.makedirs('models', exist_ok=True)
                torch.save(self.model.state_dict(), 'models/best_pytorch_lstm_model.pth')
            else:
                patience_counter += 1

            # Print progress
            if (epoch + 1) % 10 == 0:
                print(f"Epoch [{epoch+1}/{epochs}] - Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}")

            # Early stopping
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break

        # Load best model
        self.model.load_state_dict(torch.load('models/best_pytorch_lstm_model.pth'))
        print("✅ Model training completed")

        return self.training_history

    def predict(self, X):
        """Make predictions with the trained model"""
        self.model.eval()
        predictions = []

        with torch.no_grad():
            # Convert to tensor and move to device
            X_tensor = torch.FloatTensor(X).to(self.device)

            # Make predictions in batches
            batch_size = 32
            for i in range(0, len(X_tensor), batch_size):
                batch = X_tensor[i:i+batch_size]
                batch_pred = self.model(batch)
                predictions.extend(batch_pred.cpu().numpy())

        return np.array(predictions).flatten()

    def calculate_metrics(self, y_true, y_pred):
        """Calculate comprehensive performance metrics"""
        # Convert back to original scale
        y_true_orig = self.scaler.inverse_transform(y_true.reshape(-1, 1)).flatten()
        y_pred_orig = self.scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()

        # Calculate metrics
        mae = mean_absolute_error(y_true_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_true_orig, y_pred_orig))

        # Calculate percentage metrics
        mae_percent = (mae / np.mean(y_true_orig)) * 100
        mape = np.mean(np.abs((y_true_orig - y_pred_orig) / y_true_orig)) * 100
        rmse_percent = (rmse / np.mean(y_true_orig)) * 100

        # Calculate R²
        ss_res = np.sum((y_true_orig - y_pred_orig) ** 2)
        ss_tot = np.sum((y_true_orig - np.mean(y_true_orig)) ** 2)
        r2 = 1 - (ss_res / ss_tot)

        metrics = {
            'mae': mae,
            'rmse': rmse,
            'mae_percent': mae_percent,
            'mape': mape,
            'rmse_percent': rmse_percent,
            'r2': r2,
            'mean_actual': np.mean(y_true_orig),
            'std_actual': np.std(y_true_orig),
            'mean_predicted': np.mean(y_pred_orig),
            'std_predicted': np.std(y_pred_orig)
        }

        return metrics

    def evaluate_model(self, train_data, val_data, test_data):
        """Comprehensive model evaluation"""
        print(f"\n📊 EVALUATING PYTORCH LSTM MODEL PERFORMANCE")
        print("="*60)

        X_train, y_train, dates_train = train_data
        X_val, y_val, dates_val = val_data
        X_test, y_test, dates_test = test_data

        # Make predictions
        print("🔮 Making predictions...")
        train_pred = self.predict(X_train)
        val_pred = self.predict(X_val)
        test_pred = self.predict(X_test)

        # Calculate metrics for each dataset
        train_metrics = self.calculate_metrics(y_train, train_pred)
        val_metrics = self.calculate_metrics(y_val, val_pred)
        test_metrics = self.calculate_metrics(y_test, test_pred)

        # Store evaluation results
        self.evaluation_results = {
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'test_metrics': test_metrics,
            'target_mae': self.target_mae,
            'target_mape': self.target_mape,
            'model_params': {
                'sequence_length': self.sequence_length,
                'total_parameters': sum(p.numel() for p in self.model.parameters()),
                'architecture': 'Multi-layer PyTorch LSTM with BatchNorm and Dropout',
                'device': str(self.device)
            }
        }

        # Print results
        self.print_evaluation_results()

        # Create visualizations
        self.create_evaluation_plots(
            (y_train, train_pred, dates_train, "Training"),
            (y_val, val_pred, dates_val, "Validation"),
            (y_test, test_pred, dates_test, "Test")
        )

        return self.evaluation_results

    def print_evaluation_results(self):
        """Print comprehensive evaluation results"""
        print(f"\n📈 PYTORCH LSTM MODEL PERFORMANCE RESULTS")
        print("="*60)

        datasets = [
            ("Training", self.evaluation_results['train_metrics']),
            ("Validation", self.evaluation_results['val_metrics']),
            ("Test", self.evaluation_results['test_metrics'])
        ]

        for dataset_name, metrics in datasets:
            print(f"\n{dataset_name} Set Performance:")
            print(f"  MAE: ${metrics['mae']:,.2f} ({metrics['mae_percent']:.2f}%)")
            print(f"  MAPE: {metrics['mape']:.2f}%")
            print(f"  RMSE: ${metrics['rmse']:,.2f} ({metrics['rmse_percent']:.2f}%)")
            print(f"  R²: {metrics['r2']:.4f}")

            # Check target achievement
            mae_target_met = metrics['mae_percent'] < self.target_mae
            mape_target_met = metrics['mape'] < self.target_mape

            print(f"  Target Achievement:")
            print(f"    MAE <{self.target_mae}%: {'✅ ACHIEVED' if mae_target_met else '❌ NOT MET'}")
            print(f"    MAPE <{self.target_mape}%: {'✅ ACHIEVED' if mape_target_met else '❌ NOT MET'}")

        # Overall assessment
        test_metrics = self.evaluation_results['test_metrics']
        mae_achieved = test_metrics['mae_percent'] < self.target_mae
        mape_achieved = test_metrics['mape'] < self.target_mape

        print(f"\n🎯 OVERALL TARGET ASSESSMENT (Test Set):")
        print(f"   MAE Target (<{self.target_mae}%): {test_metrics['mae_percent']:.2f}% - {'✅ ACHIEVED' if mae_achieved else '❌ NOT MET'}")
        print(f"   MAPE Target (<{self.target_mape}%): {test_metrics['mape']:.2f}% - {'✅ ACHIEVED' if mape_achieved else '❌ NOT MET'}")

        if mae_achieved and mape_achieved:
            print(f"   🏆 EXCELLENT: Both targets achieved!")
        elif mae_achieved or mape_achieved:
            print(f"   ⚠️  PARTIAL: One target achieved")
        else:
            print(f"   ❌ NEEDS IMPROVEMENT: Neither target achieved")

    def create_evaluation_plots(self, *datasets):
        """Create comprehensive evaluation visualizations"""
        print(f"\n📊 Creating evaluation visualizations...")

        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('PyTorch LSTM Model Performance Evaluation', fontsize=16, fontweight='bold')

        # Plot 1: Predictions vs Actual for Test Set
        y_test, test_pred, dates_test, _ = datasets[2]  # Test data
        y_test_orig = self.scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        test_pred_orig = self.scaler.inverse_transform(test_pred.reshape(-1, 1)).flatten()

        axes[0, 0].plot(dates_test, y_test_orig, label='Actual', color='blue', alpha=0.7, linewidth=2)
        axes[0, 0].plot(dates_test, test_pred_orig, label='LSTM Prediction', color='red', alpha=0.7, linewidth=2)
        axes[0, 0].set_title('Test Set: Actual vs Predicted Revenue', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('Date')
        axes[0, 0].set_ylabel('Daily Revenue ($)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Residuals
        residuals = y_test_orig - test_pred_orig
        axes[0, 1].scatter(test_pred_orig, residuals, alpha=0.6, color='purple')
        axes[0, 1].axhline(y=0, color='red', linestyle='--', linewidth=2)
        axes[0, 1].set_title('Residuals Plot (Test Set)', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('Predicted Values')
        axes[0, 1].set_ylabel('Residuals')
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: Training History
        if self.training_history['train_loss']:
            axes[1, 0].plot(self.training_history['train_loss'], label='Training Loss', color='blue', linewidth=2)
            axes[1, 0].plot(self.training_history['val_loss'], label='Validation Loss', color='red', linewidth=2)
            axes[1, 0].set_title('Training History', fontsize=14, fontweight='bold')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Loss (MSE)')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Performance Comparison
        datasets_names = ['Training', 'Validation', 'Test']
        mae_values = [self.evaluation_results['train_metrics']['mae_percent'],
                     self.evaluation_results['val_metrics']['mae_percent'],
                     self.evaluation_results['test_metrics']['mae_percent']]
        mape_values = [self.evaluation_results['train_metrics']['mape'],
                      self.evaluation_results['val_metrics']['mape'],
                      self.evaluation_results['test_metrics']['mape']]

        x = np.arange(len(datasets_names))
        width = 0.35

        bars1 = axes[1, 1].bar(x - width/2, mae_values, width, label='MAE (%)', alpha=0.8, color='skyblue')
        bars2 = axes[1, 1].bar(x + width/2, mape_values, width, label='MAPE (%)', alpha=0.8, color='lightcoral')
        axes[1, 1].axhline(y=self.target_mae, color='blue', linestyle='--', alpha=0.7, linewidth=2, label=f'MAE Target ({self.target_mae}%)')
        axes[1, 1].axhline(y=self.target_mape, color='red', linestyle='--', alpha=0.7, linewidth=2, label=f'MAPE Target ({self.target_mape}%)')
        axes[1, 1].set_title('Performance Metrics Comparison', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('Dataset')
        axes[1, 1].set_ylabel('Error (%)')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(datasets_names)
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # Add value labels on bars
        for bar in bars1:
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                           f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')

        for bar in bars2:
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                           f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig('pytorch_lstm_model_evaluation.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Evaluation plots saved as 'pytorch_lstm_model_evaluation.png'")

    def save_model_and_results(self):
        """Save trained model and evaluation results"""
        print(f"\n💾 Saving PyTorch LSTM model and results...")

        # Create models directory
        os.makedirs('models', exist_ok=True)

        # Save the trained model
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'model_config': {
                'input_size': self.model.lstm.input_size,
                'hidden_size': self.model.hidden_size,
                'num_layers': self.model.num_layers,
                'dropout': 0.3
            },
            'sequence_length': self.sequence_length
        }, 'models/pytorch_lstm_complete_model.pth')

        # Save scalers
        with open('models/pytorch_lstm_scalers.pkl', 'wb') as f:
            pickle.dump({
                'target_scaler': self.scaler,
                'feature_scaler': self.feature_scaler,
                'sequence_length': self.sequence_length
            }, f)

        # Save evaluation results
        with open('models/pytorch_lstm_evaluation_results.json', 'w') as f:
            json.dump(self.evaluation_results, f, indent=2, default=str)

        # Save comprehensive results as pickle
        with open('models/pytorch_lstm_comprehensive_results.pkl', 'wb') as f:
            pickle.dump({
                'model_path': 'models/pytorch_lstm_complete_model.pth',
                'scalers': {
                    'target_scaler': self.scaler,
                    'feature_scaler': self.feature_scaler
                },
                'evaluation_results': self.evaluation_results,
                'training_history': self.training_history,
                'model_config': {
                    'sequence_length': self.sequence_length,
                    'target_mae': self.target_mae,
                    'target_mape': self.target_mape,
                    'device': str(self.device)
                }
            }, f)

        print("✅ PyTorch LSTM model and results saved successfully:")
        print("   - models/pytorch_lstm_complete_model.pth")
        print("   - models/pytorch_lstm_scalers.pkl")
        print("   - models/pytorch_lstm_evaluation_results.json")
        print("   - models/pytorch_lstm_comprehensive_results.pkl")


def main():
    """Main execution function for PyTorch LSTM forecasting"""
    print("🚀 PYTORCH LSTM TIME SERIES FORECASTING")
    print("="*60)
    print("Target Performance: MAE <5%, MAPE <5%")
    print("="*60)

    # Initialize forecaster
    forecaster = PyTorchLSTMForecaster(sequence_length=30, target_mae=5.0, target_mape=5.0)

    try:
        # Step 1: Load and prepare data
        data = forecaster.load_and_prepare_data()

        # Step 2: Prepare sequences
        X, y, dates = forecaster.prepare_sequences(data)

        # Step 3: Split data
        train_data, val_data, test_data = forecaster.split_data(X, y, dates)

        # Step 4: Train model
        print(f"\n🎯 Starting PyTorch LSTM training...")
        history = forecaster.train_model(train_data, val_data, epochs=100, batch_size=32)

        # Step 5: Evaluate model
        results = forecaster.evaluate_model(train_data, val_data, test_data)

        # Step 6: Save model and results
        forecaster.save_model_and_results()

        # Final summary
        test_metrics = results['test_metrics']
        mae_achieved = test_metrics['mae_percent'] < 5.0
        mape_achieved = test_metrics['mape'] < 5.0

        print(f"\n🏁 PYTORCH LSTM FORECASTING COMPLETED")
        print("="*60)
        print(f"Final Test Performance:")
        print(f"  MAE: {test_metrics['mae_percent']:.2f}% (Target: <5%) - {'✅' if mae_achieved else '❌'}")
        print(f"  MAPE: {test_metrics['mape']:.2f}% (Target: <5%) - {'✅' if mape_achieved else '❌'}")
        print(f"  RMSE: {test_metrics['rmse_percent']:.2f}%")
        print(f"  R²: {test_metrics['r2']:.4f}")

        if mae_achieved and mape_achieved:
            print(f"\n🎉 SUCCESS: Both performance targets achieved!")
            grade = "A+"
        elif mae_achieved or mape_achieved:
            print(f"\n⚠️  PARTIAL SUCCESS: One target achieved")
            grade = "B"
        else:
            print(f"\n❌ TARGETS NOT MET: Model needs improvement")
            grade = "C"

        print(f"Overall Grade: {grade}")

        # Compare with ARIMA results if available
        try:
            with open('models/model_evaluation_comprehensive.json', 'r') as f:
                arima_results = json.load(f)

            print(f"\n📊 COMPARISON WITH ARIMA MODELS:")
            print("="*40)
            print(f"PyTorch LSTM vs ARIMA(1,1,1):")
            arima_111_mae = arima_results['arima_1_1_1']['test_metrics']['mae_percent']
            arima_111_mape = arima_results['arima_1_1_1']['test_metrics']['mape']

            print(f"  MAE: {test_metrics['mae_percent']:.2f}% vs {arima_111_mae:.2f}% (ARIMA)")
            print(f"  MAPE: {test_metrics['mape']:.2f}% vs {arima_111_mape:.2f}% (ARIMA)")

            mae_improvement = ((arima_111_mae - test_metrics['mae_percent']) / arima_111_mae) * 100
            mape_improvement = ((arima_111_mape - test_metrics['mape']) / arima_111_mape) * 100

            print(f"  MAE Improvement: {mae_improvement:.1f}%")
            print(f"  MAPE Improvement: {mape_improvement:.1f}%")

        except FileNotFoundError:
            print(f"\n📊 ARIMA comparison data not available")

        return forecaster, results

    except Exception as e:
        print(f"❌ Error during PyTorch LSTM training: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    forecaster, results = main()
