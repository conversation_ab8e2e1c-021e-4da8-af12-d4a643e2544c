#!/usr/bin/env python3
"""
Advanced LSTM-Style Time Series Forecasting Model
Implementation using scikit-learn and numpy for daily revenue forecasting
Target performance: <5% MAE and MAPE
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.neural_network import MLPRegressor
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge
import warnings
import pickle
import json
from datetime import datetime, timedelta
import os

warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)

class AdvancedLSTMStyleForecaster:
    """Advanced LSTM-style Forecasting Model using scikit-learn"""
    
    def __init__(self, sequence_length=30, target_mae=5.0, target_mape=5.0):
        self.sequence_length = sequence_length
        self.target_mae = target_mae
        self.target_mape = target_mape
        self.models = {}
        self.scaler = None
        self.feature_scaler = None
        self.evaluation_results = {}
        
    def load_and_prepare_data(self):
        """Load and prepare data for LSTM-style training"""
        print("="*60)
        print("LOADING AND PREPARING DATA FOR ADVANCED FORECASTING")
        print("="*60)
        
        # Load the cleaned daily revenue time series
        print("📊 Loading daily revenue time series...")
        daily_ts = pd.read_csv('daily_revenue_timeseries.csv')
        daily_ts['Date'] = pd.to_datetime(daily_ts['Date'])
        daily_ts = daily_ts.sort_values('Date').reset_index(drop=True)
        
        print(f"✅ Loaded {len(daily_ts)} daily observations")
        print(f"   Date range: {daily_ts['Date'].min()} to {daily_ts['Date'].max()}")
        print(f"   Revenue range: ${daily_ts['DailyRevenue'].min():,.2f} to ${daily_ts['DailyRevenue'].max():,.2f}")
        
        # Create advanced features
        print("\n🔧 Creating advanced features...")
        daily_ts = self.create_advanced_features(daily_ts)
        
        # Handle missing dates
        print("\n📅 Creating continuous time series...")
        daily_ts = self.create_continuous_timeseries(daily_ts)
        
        print(f"✅ Final dataset: {len(daily_ts)} observations")
        return daily_ts
    
    def create_advanced_features(self, df):
        """Create comprehensive features for forecasting"""
        df = df.copy()
        
        # Time-based features
        df['Year'] = df['Date'].dt.year
        df['Month'] = df['Date'].dt.month
        df['Day'] = df['Date'].dt.day
        df['DayOfWeek'] = df['Date'].dt.dayofweek
        df['DayOfYear'] = df['Date'].dt.dayofyear
        df['WeekOfYear'] = df['Date'].dt.isocalendar().week
        df['Quarter'] = df['Date'].dt.quarter
        df['IsWeekend'] = (df['DayOfWeek'] >= 5).astype(int)
        df['IsMonthStart'] = df['Date'].dt.is_month_start.astype(int)
        df['IsMonthEnd'] = df['Date'].dt.is_month_end.astype(int)
        
        # Cyclical encoding for time features
        df['Month_sin'] = np.sin(2 * np.pi * df['Month'] / 12)
        df['Month_cos'] = np.cos(2 * np.pi * df['Month'] / 12)
        df['DayOfWeek_sin'] = np.sin(2 * np.pi * df['DayOfWeek'] / 7)
        df['DayOfWeek_cos'] = np.cos(2 * np.pi * df['DayOfWeek'] / 7)
        df['DayOfYear_sin'] = np.sin(2 * np.pi * df['DayOfYear'] / 365)
        df['DayOfYear_cos'] = np.cos(2 * np.pi * df['DayOfYear'] / 365)
        
        # Lag features (multiple lags)
        for lag in [1, 2, 3, 7, 14, 21, 30]:
            df[f'Revenue_lag_{lag}'] = df['DailyRevenue'].shift(lag)
            df[f'Quantity_lag_{lag}'] = df['DailyQuantity'].shift(lag)
            df[f'Transactions_lag_{lag}'] = df['DailyTransactions'].shift(lag)
        
        # Rolling statistics (multiple windows)
        for window in [3, 7, 14, 21, 30, 60]:
            df[f'Revenue_rolling_mean_{window}'] = df['DailyRevenue'].rolling(window=window).mean()
            df[f'Revenue_rolling_std_{window}'] = df['DailyRevenue'].rolling(window=window).std()
            df[f'Revenue_rolling_min_{window}'] = df['DailyRevenue'].rolling(window=window).min()
            df[f'Revenue_rolling_max_{window}'] = df['DailyRevenue'].rolling(window=window).max()
            df[f'Revenue_rolling_median_{window}'] = df['DailyRevenue'].rolling(window=window).median()
        
        # Exponential moving averages
        for alpha in [0.1, 0.2, 0.3, 0.5, 0.7]:
            df[f'Revenue_ema_{alpha}'] = df['DailyRevenue'].ewm(alpha=alpha).mean()
        
        # Revenue ratios and differences
        df['Revenue_pct_change'] = df['DailyRevenue'].pct_change()
        df['Revenue_diff'] = df['DailyRevenue'].diff()
        df['Revenue_diff_2'] = df['DailyRevenue'].diff(2)
        df['Avg_transaction_value'] = df['DailyRevenue'] / df['DailyTransactions']
        df['Avg_items_per_transaction'] = df['DailyQuantity'] / df['DailyTransactions']
        
        # Interaction features
        df['Revenue_Quantity_ratio'] = df['DailyRevenue'] / df['DailyQuantity']
        df['Month_DayOfWeek'] = df['Month'] * df['DayOfWeek']
        df['Quarter_Month'] = df['Quarter'] * df['Month']
        
        # Volatility measures
        for window in [7, 14, 30]:
            df[f'Revenue_volatility_{window}'] = df['DailyRevenue'].rolling(window=window).std() / df['DailyRevenue'].rolling(window=window).mean()
        
        print(f"   Created {len(df.columns) - 4} additional features")
        return df
    
    def create_continuous_timeseries(self, df):
        """Create continuous time series by filling missing dates"""
        # Create complete date range
        date_range = pd.date_range(start=df['Date'].min(), end=df['Date'].max(), freq='D')
        complete_df = pd.DataFrame({'Date': date_range})
        
        # Merge with existing data
        df_complete = complete_df.merge(df, on='Date', how='left')
        
        # Forward fill and interpolate missing values
        numeric_cols = df_complete.select_dtypes(include=[np.number]).columns
        
        # Forward fill first
        df_complete[numeric_cols] = df_complete[numeric_cols].fillna(method='ffill')
        
        # Then interpolate for smoother transitions
        for col in numeric_cols:
            if col in df_complete.columns:
                df_complete[col] = df_complete[col].interpolate(method='linear')
        
        # Fill any remaining NaN values
        df_complete = df_complete.fillna(method='bfill').fillna(method='ffill')
        
        return df_complete
    
    def prepare_sequences(self, data, target_col='DailyRevenue'):
        """Prepare sequences for LSTM-style training"""
        print(f"\n🔄 Preparing sequences (length={self.sequence_length})...")
        
        # Select features for training (exclude Date and target)
        feature_cols = [col for col in data.columns if col not in ['Date']]
        
        # Remove target from features for X, but keep for sequence creation
        X_feature_cols = [col for col in feature_cols if col != target_col]
        
        # Scale the data
        self.scaler = MinMaxScaler()
        self.feature_scaler = MinMaxScaler()
        
        # Scale target variable
        target_data = data[target_col].values.reshape(-1, 1)
        scaled_target = self.scaler.fit_transform(target_data)
        
        # Scale features
        feature_data = data[X_feature_cols].values
        scaled_features = self.feature_scaler.fit_transform(feature_data)
        
        # Create sequences for LSTM-style input
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_features)):
            # Flatten the sequence for traditional ML models
            sequence_flat = scaled_features[i-self.sequence_length:i].flatten()
            
            # Add current time step features (excluding target)
            current_features = scaled_features[i]
            
            # Combine sequence and current features
            combined_features = np.concatenate([sequence_flat, current_features])
            
            X.append(combined_features)
            y.append(scaled_target[i, 0])
        
        X, y = np.array(X), np.array(y)
        
        print(f"✅ Created {len(X)} sequences")
        print(f"   Input shape: {X.shape}")
        print(f"   Output shape: {y.shape}")
        
        return X, y, data.iloc[self.sequence_length:]['Date'].values
    
    def split_data(self, X, y, dates, train_ratio=0.7, val_ratio=0.15):
        """Split data into train, validation, and test sets"""
        n_samples = len(X)
        train_size = int(n_samples * train_ratio)
        val_size = int(n_samples * val_ratio)
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        dates_train = dates[:train_size]
        
        X_val = X[train_size:train_size + val_size]
        y_val = y[train_size:train_size + val_size]
        dates_val = dates[train_size:train_size + val_size]
        
        X_test = X[train_size + val_size:]
        y_test = y[train_size + val_size:]
        dates_test = dates[train_size + val_size:]
        
        print(f"\n📊 Data split:")
        print(f"   Training: {len(X_train)} samples ({train_ratio*100:.1f}%)")
        print(f"   Validation: {len(X_val)} samples ({val_ratio*100:.1f}%)")
        print(f"   Testing: {len(X_test)} samples ({(1-train_ratio-val_ratio)*100:.1f}%)")
        
        return (X_train, y_train, dates_train), (X_val, y_val, dates_val), (X_test, y_test, dates_test)

    def train_ensemble_models(self, train_data, val_data):
        """Train ensemble of advanced models"""
        X_train, y_train, _ = train_data
        X_val, y_val, _ = val_data

        print(f"\n🚀 Training ensemble of advanced models...")

        # Define models with optimized hyperparameters
        models_config = {
            'neural_network': MLPRegressor(
                hidden_layer_sizes=(200, 100, 50),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                max_iter=1000,
                early_stopping=True,
                validation_fraction=0.1,
                n_iter_no_change=20,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=6,
                min_samples_split=10,
                min_samples_leaf=5,
                subsample=0.8,
                random_state=42
            ),
            'random_forest': RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=10,
                min_samples_leaf=5,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1
            ),
            'ridge_regression': Ridge(
                alpha=1.0,
                random_state=42
            )
        }

        # Train each model
        for name, model in models_config.items():
            print(f"   Training {name}...")
            model.fit(X_train, y_train)

            # Validate
            val_pred = model.predict(X_val)
            val_score = mean_squared_error(y_val, val_pred)

            self.models[name] = {
                'model': model,
                'val_score': val_score
            }

            print(f"   ✅ {name} - Validation MSE: {val_score:.6f}")

        # Create ensemble model (weighted average based on validation performance)
        val_scores = [self.models[name]['val_score'] for name in self.models.keys()]
        weights = 1 / (np.array(val_scores) + 1e-8)  # Inverse of error
        weights = weights / weights.sum()  # Normalize

        self.ensemble_weights = dict(zip(self.models.keys(), weights))

        print(f"\n✅ Ensemble weights:")
        for name, weight in self.ensemble_weights.items():
            print(f"   {name}: {weight:.3f}")

        return self.models

    def predict_ensemble(self, X):
        """Make ensemble predictions"""
        predictions = {}

        # Get predictions from each model
        for name, model_info in self.models.items():
            predictions[name] = model_info['model'].predict(X)

        # Weighted ensemble prediction
        ensemble_pred = np.zeros(len(X))
        for name, pred in predictions.items():
            ensemble_pred += self.ensemble_weights[name] * pred

        return ensemble_pred, predictions

    def calculate_metrics(self, y_true, y_pred):
        """Calculate comprehensive performance metrics"""
        # Convert back to original scale
        y_true_orig = self.scaler.inverse_transform(y_true.reshape(-1, 1)).flatten()
        y_pred_orig = self.scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()

        # Calculate metrics
        mae = mean_absolute_error(y_true_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_true_orig, y_pred_orig))

        # Calculate percentage metrics
        mae_percent = (mae / np.mean(y_true_orig)) * 100
        mape = np.mean(np.abs((y_true_orig - y_pred_orig) / y_true_orig)) * 100
        rmse_percent = (rmse / np.mean(y_true_orig)) * 100

        # Calculate R²
        ss_res = np.sum((y_true_orig - y_pred_orig) ** 2)
        ss_tot = np.sum((y_true_orig - np.mean(y_true_orig)) ** 2)
        r2 = 1 - (ss_res / ss_tot)

        metrics = {
            'mae': mae,
            'rmse': rmse,
            'mae_percent': mae_percent,
            'mape': mape,
            'rmse_percent': rmse_percent,
            'r2': r2,
            'mean_actual': np.mean(y_true_orig),
            'std_actual': np.std(y_true_orig),
            'mean_predicted': np.mean(y_pred_orig),
            'std_predicted': np.std(y_pred_orig)
        }

        return metrics

    def evaluate_models(self, train_data, val_data, test_data):
        """Comprehensive model evaluation"""
        print(f"\n📊 EVALUATING ADVANCED FORECASTING MODELS")
        print("="*60)

        X_train, y_train, dates_train = train_data
        X_val, y_val, dates_val = val_data
        X_test, y_test, dates_test = test_data

        # Make predictions
        print("🔮 Making ensemble predictions...")
        train_pred, train_individual = self.predict_ensemble(X_train)
        val_pred, val_individual = self.predict_ensemble(X_val)
        test_pred, test_individual = self.predict_ensemble(X_test)

        # Calculate metrics for ensemble
        train_metrics = self.calculate_metrics(y_train, train_pred)
        val_metrics = self.calculate_metrics(y_val, val_pred)
        test_metrics = self.calculate_metrics(y_test, test_pred)

        # Calculate metrics for individual models
        individual_metrics = {}
        for model_name in self.models.keys():
            individual_metrics[model_name] = {
                'train': self.calculate_metrics(y_train, train_individual[model_name]),
                'val': self.calculate_metrics(y_val, val_individual[model_name]),
                'test': self.calculate_metrics(y_test, test_individual[model_name])
            }

        # Store evaluation results
        self.evaluation_results = {
            'ensemble_metrics': {
                'train_metrics': train_metrics,
                'val_metrics': val_metrics,
                'test_metrics': test_metrics
            },
            'individual_metrics': individual_metrics,
            'target_mae': self.target_mae,
            'target_mape': self.target_mape,
            'model_params': {
                'sequence_length': self.sequence_length,
                'ensemble_weights': self.ensemble_weights,
                'models_used': list(self.models.keys())
            }
        }

        # Print results
        self.print_evaluation_results()

        # Create visualizations
        self.create_evaluation_plots(
            (y_train, train_pred, dates_train, "Training"),
            (y_val, val_pred, dates_val, "Validation"),
            (y_test, test_pred, dates_test, "Test")
        )

        return self.evaluation_results

    def print_evaluation_results(self):
        """Print comprehensive evaluation results"""
        print(f"\n📈 ADVANCED FORECASTING MODEL PERFORMANCE RESULTS")
        print("="*60)

        # Ensemble results
        ensemble_metrics = self.evaluation_results['ensemble_metrics']
        datasets = [
            ("Training", ensemble_metrics['train_metrics']),
            ("Validation", ensemble_metrics['val_metrics']),
            ("Test", ensemble_metrics['test_metrics'])
        ]

        print(f"\n🎯 ENSEMBLE MODEL PERFORMANCE:")
        for dataset_name, metrics in datasets:
            print(f"\n{dataset_name} Set Performance:")
            print(f"  MAE: ${metrics['mae']:,.2f} ({metrics['mae_percent']:.2f}%)")
            print(f"  MAPE: {metrics['mape']:.2f}%")
            print(f"  RMSE: ${metrics['rmse']:,.2f} ({metrics['rmse_percent']:.2f}%)")
            print(f"  R²: {metrics['r2']:.4f}")

            # Check target achievement
            mae_target_met = metrics['mae_percent'] < self.target_mae
            mape_target_met = metrics['mape'] < self.target_mape

            print(f"  Target Achievement:")
            print(f"    MAE <{self.target_mae}%: {'✅ ACHIEVED' if mae_target_met else '❌ NOT MET'}")
            print(f"    MAPE <{self.target_mape}%: {'✅ ACHIEVED' if mape_target_met else '❌ NOT MET'}")

        # Individual model performance on test set
        print(f"\n🔍 INDIVIDUAL MODEL PERFORMANCE (Test Set):")
        individual_metrics = self.evaluation_results['individual_metrics']
        for model_name, metrics in individual_metrics.items():
            test_metrics = metrics['test']
            mae_achieved = test_metrics['mae_percent'] < self.target_mae
            mape_achieved = test_metrics['mape'] < self.target_mape

            print(f"\n{model_name.replace('_', ' ').title()}:")
            print(f"  MAE: {test_metrics['mae_percent']:.2f}% - {'✅' if mae_achieved else '❌'}")
            print(f"  MAPE: {test_metrics['mape']:.2f}% - {'✅' if mape_achieved else '❌'}")
            print(f"  R²: {test_metrics['r2']:.4f}")

        # Overall assessment
        test_metrics = ensemble_metrics['test_metrics']
        mae_achieved = test_metrics['mae_percent'] < self.target_mae
        mape_achieved = test_metrics['mape'] < self.target_mape

        print(f"\n🎯 OVERALL TARGET ASSESSMENT (Ensemble Test Set):")
        print(f"   MAE Target (<{self.target_mae}%): {test_metrics['mae_percent']:.2f}% - {'✅ ACHIEVED' if mae_achieved else '❌ NOT MET'}")
        print(f"   MAPE Target (<{self.target_mape}%): {test_metrics['mape']:.2f}% - {'✅ ACHIEVED' if mape_achieved else '❌ NOT MET'}")

        if mae_achieved and mape_achieved:
            print(f"   🏆 EXCELLENT: Both targets achieved!")
        elif mae_achieved or mape_achieved:
            print(f"   ⚠️  PARTIAL: One target achieved")
        else:
            print(f"   ❌ NEEDS IMPROVEMENT: Neither target achieved")

    def create_evaluation_plots(self, *datasets):
        """Create comprehensive evaluation visualizations"""
        print(f"\n📊 Creating evaluation visualizations...")

        fig, axes = plt.subplots(2, 3, figsize=(24, 16))
        fig.suptitle('Advanced LSTM-Style Forecasting Model Performance', fontsize=16, fontweight='bold')

        # Plot 1: Predictions vs Actual for Test Set
        y_test, test_pred, dates_test, _ = datasets[2]  # Test data
        y_test_orig = self.scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        test_pred_orig = self.scaler.inverse_transform(test_pred.reshape(-1, 1)).flatten()

        axes[0, 0].plot(dates_test, y_test_orig, label='Actual', color='blue', alpha=0.7, linewidth=2)
        axes[0, 0].plot(dates_test, test_pred_orig, label='Ensemble Prediction', color='red', alpha=0.7, linewidth=2)
        axes[0, 0].set_title('Test Set: Actual vs Predicted Revenue', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('Date')
        axes[0, 0].set_ylabel('Daily Revenue ($)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].tick_params(axis='x', rotation=45)

        # Plot 2: Residuals
        residuals = y_test_orig - test_pred_orig
        axes[0, 1].scatter(test_pred_orig, residuals, alpha=0.6, color='purple')
        axes[0, 1].axhline(y=0, color='red', linestyle='--', linewidth=2)
        axes[0, 1].set_title('Residuals Plot (Test Set)', fontsize=12, fontweight='bold')
        axes[0, 1].set_xlabel('Predicted Values')
        axes[0, 1].set_ylabel('Residuals')
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: Performance Comparison (Ensemble vs Individual)
        model_names = ['Ensemble'] + list(self.evaluation_results['individual_metrics'].keys())
        mae_values = [self.evaluation_results['ensemble_metrics']['test_metrics']['mae_percent']]
        mape_values = [self.evaluation_results['ensemble_metrics']['test_metrics']['mape']]

        for model_name in self.evaluation_results['individual_metrics'].keys():
            mae_values.append(self.evaluation_results['individual_metrics'][model_name]['test']['mae_percent'])
            mape_values.append(self.evaluation_results['individual_metrics'][model_name]['test']['mape'])

        x = np.arange(len(model_names))
        width = 0.35

        bars1 = axes[0, 2].bar(x - width/2, mae_values, width, label='MAE (%)', alpha=0.8, color='skyblue')
        bars2 = axes[0, 2].bar(x + width/2, mape_values, width, label='MAPE (%)', alpha=0.8, color='lightcoral')
        axes[0, 2].axhline(y=self.target_mae, color='blue', linestyle='--', alpha=0.7, linewidth=2, label=f'MAE Target ({self.target_mae}%)')
        axes[0, 2].axhline(y=self.target_mape, color='red', linestyle='--', alpha=0.7, linewidth=2, label=f'MAPE Target ({self.target_mape}%)')
        axes[0, 2].set_title('Model Performance Comparison', fontsize=12, fontweight='bold')
        axes[0, 2].set_xlabel('Model')
        axes[0, 2].set_ylabel('Error (%)')
        axes[0, 2].set_xticks(x)
        axes[0, 2].set_xticklabels([name.replace('_', ' ').title() for name in model_names], rotation=45)
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)

        # Plot 4: Prediction Distribution
        axes[1, 0].hist(y_test_orig, bins=30, alpha=0.7, label='Actual', color='blue', density=True)
        axes[1, 0].hist(test_pred_orig, bins=30, alpha=0.7, label='Predicted', color='red', density=True)
        axes[1, 0].set_title('Distribution Comparison', fontsize=12, fontweight='bold')
        axes[1, 0].set_xlabel('Daily Revenue ($)')
        axes[1, 0].set_ylabel('Density')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 5: Error Distribution
        axes[1, 1].hist(residuals, bins=30, alpha=0.7, color='green', density=True)
        axes[1, 1].axvline(x=0, color='red', linestyle='--', linewidth=2)
        axes[1, 1].set_title('Residuals Distribution', fontsize=12, fontweight='bold')
        axes[1, 1].set_xlabel('Residuals')
        axes[1, 1].set_ylabel('Density')
        axes[1, 1].grid(True, alpha=0.3)

        # Plot 6: Scatter plot (Actual vs Predicted)
        axes[1, 2].scatter(y_test_orig, test_pred_orig, alpha=0.6, color='orange')
        min_val = min(y_test_orig.min(), test_pred_orig.min())
        max_val = max(y_test_orig.max(), test_pred_orig.max())
        axes[1, 2].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
        axes[1, 2].set_title('Actual vs Predicted Scatter', fontsize=12, fontweight='bold')
        axes[1, 2].set_xlabel('Actual Values')
        axes[1, 2].set_ylabel('Predicted Values')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('advanced_lstm_model_evaluation.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Evaluation plots saved as 'advanced_lstm_model_evaluation.png'")

    def save_model_and_results(self):
        """Save trained models and evaluation results"""
        print(f"\n💾 Saving advanced forecasting models and results...")

        # Create models directory
        os.makedirs('models', exist_ok=True)

        # Save all models
        with open('models/advanced_lstm_models.pkl', 'wb') as f:
            pickle.dump({
                'models': self.models,
                'ensemble_weights': self.ensemble_weights,
                'scalers': {
                    'target_scaler': self.scaler,
                    'feature_scaler': self.feature_scaler
                },
                'sequence_length': self.sequence_length
            }, f)

        # Save evaluation results
        with open('models/advanced_lstm_evaluation_results.json', 'w') as f:
            json.dump(self.evaluation_results, f, indent=2, default=str)

        # Save comprehensive results as pickle
        with open('models/advanced_lstm_comprehensive_results.pkl', 'wb') as f:
            pickle.dump({
                'models': self.models,
                'ensemble_weights': self.ensemble_weights,
                'scalers': {
                    'target_scaler': self.scaler,
                    'feature_scaler': self.feature_scaler
                },
                'evaluation_results': self.evaluation_results,
                'model_config': {
                    'sequence_length': self.sequence_length,
                    'target_mae': self.target_mae,
                    'target_mape': self.target_mape
                }
            }, f)

        print("✅ Advanced LSTM models and results saved successfully:")
        print("   - models/advanced_lstm_models.pkl")
        print("   - models/advanced_lstm_evaluation_results.json")
        print("   - models/advanced_lstm_comprehensive_results.pkl")


def main():
    """Main execution function for advanced LSTM-style forecasting"""
    print("🚀 ADVANCED LSTM-STYLE TIME SERIES FORECASTING")
    print("="*60)
    print("Target Performance: MAE <5%, MAPE <5%")
    print("Using Ensemble of: Neural Network, Gradient Boosting, Random Forest, Ridge Regression")
    print("="*60)

    # Initialize forecaster
    forecaster = AdvancedLSTMStyleForecaster(sequence_length=30, target_mae=5.0, target_mape=5.0)

    try:
        # Step 1: Load and prepare data
        data = forecaster.load_and_prepare_data()

        # Step 2: Prepare sequences
        X, y, dates = forecaster.prepare_sequences(data)

        # Step 3: Split data
        train_data, val_data, test_data = forecaster.split_data(X, y, dates)

        # Step 4: Train ensemble models
        print(f"\n🎯 Starting ensemble model training...")
        models = forecaster.train_ensemble_models(train_data, val_data)

        # Step 5: Evaluate models
        results = forecaster.evaluate_models(train_data, val_data, test_data)

        # Step 6: Save models and results
        forecaster.save_model_and_results()

        # Final summary
        test_metrics = results['ensemble_metrics']['test_metrics']
        mae_achieved = test_metrics['mae_percent'] < 5.0
        mape_achieved = test_metrics['mape'] < 5.0

        print(f"\n🏁 ADVANCED LSTM-STYLE FORECASTING COMPLETED")
        print("="*60)
        print(f"Final Ensemble Test Performance:")
        print(f"  MAE: {test_metrics['mae_percent']:.2f}% (Target: <5%) - {'✅' if mae_achieved else '❌'}")
        print(f"  MAPE: {test_metrics['mape']:.2f}% (Target: <5%) - {'✅' if mape_achieved else '❌'}")
        print(f"  RMSE: {test_metrics['rmse_percent']:.2f}%")
        print(f"  R²: {test_metrics['r2']:.4f}")

        if mae_achieved and mape_achieved:
            print(f"\n🎉 SUCCESS: Both performance targets achieved!")
            grade = "A+"
        elif mae_achieved or mape_achieved:
            print(f"\n⚠️  PARTIAL SUCCESS: One target achieved")
            grade = "B"
        else:
            print(f"\n❌ TARGETS NOT MET: Model needs improvement")
            grade = "C"

        print(f"Overall Grade: {grade}")

        # Compare with ARIMA results if available
        try:
            with open('models/model_evaluation_comprehensive.json', 'r') as f:
                arima_results = json.load(f)

            print(f"\n📊 COMPARISON WITH ARIMA MODELS:")
            print("="*40)
            print(f"Advanced LSTM vs ARIMA(1,1,1):")
            arima_111_mae = arima_results['arima_1_1_1']['test_metrics']['mae_percent']
            arima_111_mape = arima_results['arima_1_1_1']['test_metrics']['mape']

            print(f"  MAE: {test_metrics['mae_percent']:.2f}% vs {arima_111_mae:.2f}% (ARIMA)")
            print(f"  MAPE: {test_metrics['mape']:.2f}% vs {arima_111_mape:.2f}% (ARIMA)")

            mae_improvement = ((arima_111_mae - test_metrics['mae_percent']) / arima_111_mae) * 100
            mape_improvement = ((arima_111_mape - test_metrics['mape']) / arima_111_mape) * 100

            print(f"  MAE Improvement: {mae_improvement:.1f}%")
            print(f"  MAPE Improvement: {mape_improvement:.1f}%")

            if mae_improvement > 0 and mape_improvement > 0:
                print(f"  🏆 Advanced LSTM outperforms ARIMA on both metrics!")
            elif mae_improvement > 0 or mape_improvement > 0:
                print(f"  ⚡ Advanced LSTM outperforms ARIMA on one metric")
            else:
                print(f"  📊 ARIMA performs better on both metrics")

        except FileNotFoundError:
            print(f"\n📊 ARIMA comparison data not available")

        # Best individual model
        best_individual = min(results['individual_metrics'].items(),
                            key=lambda x: x[1]['test']['mae_percent'])
        print(f"\n🥇 Best Individual Model: {best_individual[0].replace('_', ' ').title()}")
        print(f"   MAE: {best_individual[1]['test']['mae_percent']:.2f}%")
        print(f"   MAPE: {best_individual[1]['test']['mape']:.2f}%")

        return forecaster, results

    except Exception as e:
        print(f"❌ Error during advanced LSTM training: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    forecaster, results = main()
